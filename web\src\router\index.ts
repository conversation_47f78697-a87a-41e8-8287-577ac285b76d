import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue')
    },
    {
      path: '/meeting/:roomId',
      name: 'MeetingRoom',
      component: () => import('@/views/MeetingRoom.vue'),
      props: true
    }
  ]
})

// Remove auth check for meeting app

export default router
