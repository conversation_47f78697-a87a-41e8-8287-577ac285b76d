package service

import (
	"context"
	"meeting/internal/entity"
	"meeting/internal/repository"

	"go.uber.org/zap"
)

// UserService 用户服务接口
type UserService interface {
	CreateUser(ctx context.Context, user *entity.User) error
	GetUser(ctx context.Context, id uint) (*entity.User, error)
	UpdateUser(ctx context.Context, user *entity.User) error
	DeleteUser(ctx context.Context, id uint) error
}

// userService 用户服务实现
type userService struct {
	userRepo repository.UserRepository
	logger   *zap.SugaredLogger
}

// NewUserService 创建用户服务
func NewUserService(userRepo repository.UserRepository, logger *zap.SugaredLogger) UserService {
	return &userService{
		userRepo: userRepo,
		logger:   logger,
	}
}

func (s *userService) CreateUser(ctx context.Context, user *entity.User) error {
	s.logger.Infow("Creating user", "user", user)
	return s.userRepo.Create(ctx, user)
}

func (s *userService) GetUser(ctx context.Context, id uint) (*entity.User, error) {
	s.logger.Infow("Getting user", "id", id)
	return s.userRepo.GetByID(ctx, id)
}

func (s *userService) UpdateUser(ctx context.Context, user *entity.User) error {
	s.logger.Infow("Updating user", "user", user)
	return s.userRepo.Update(ctx, user)
}

func (s *userService) DeleteUser(ctx context.Context, id uint) error {
	s.logger.Infow("Deleting user", "id", id)
	return s.userRepo.Delete(ctx, id)
}
