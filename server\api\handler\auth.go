package handler

import (
	"context"
	"meeting/internal/entity"
	"meeting/internal/service"
	"meeting/pkg/api"
	"meeting/pkg/core"
	"meeting/pkg/passport"
	"meeting/pkg/services"
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

const (
	RedirectURIKey = "redirect_uri"
	UserKey        = "user"
)

type authHandler struct {
	authService *service.AuthService
}

var Auth = &authHandler{}

func (a *authHandler) getAuthService() *service.AuthService {
	if a.authService == nil {
		a.authService = services.GetServiceManager().GetAuthService()
	}
	return a.authService
}

func (a *authHandler) Login(ctx *gin.Context) {
	session := sessions.Default(ctx)
	session.Set(RedirectURIKey, ctx.Query("redirect_uri"))
	_ = session.Save()
	ctx.Redirect(http.StatusFound, passport.Redirect("state"))
}

func (a *authHand<PERSON>) LoginCallback(ctx *gin.Context) {
	session := sessions.Default(ctx)
	redirectURI := "/"
	if r := session.Get(RedirectURIKey); r != nil {
		redirectURI = r.(string)
	}
	session.Delete(RedirectURIKey)
	code := ctx.Query("code")
	if code == "" {
		_ = session.Save()
		ctx.Redirect(http.StatusFound, redirectURI)
		return
	}

	token, err := passport.Exchange(context.Background(), code)
	if err != nil {
		_ = session.Save()
		ctx.Redirect(http.StatusFound, redirectURI)
		return
	}

	var u *entity.User
	info, err := passport.GetUserInfo(token)
	if err != nil {
		_ = session.Save()
		ctx.JSON(http.StatusBadRequest, err)
		return
	}

	// 创建或更新用户信息
	userInfo := &entity.User{
		UUID:     info.UUID,
		Avatar:   info.Avatar,
		Email:    info.Email,
		Nickname: info.Nickname,
		Name:     info.Name,
	}

	u, err = a.getAuthService().CreateOrUpdateUser(userInfo)
	if err != nil {
		core.Logger().Errorf("保存用户信息失败: %v", err)
		_ = session.Save()
		ctx.Redirect(http.StatusFound, redirectURI)
		return
	}

	// 设置Session
	session.Set(UserKey, u.ID)
	_ = session.Save()
	ctx.Redirect(http.StatusFound, redirectURI)
}

func (a *authHandler) Token(ctx *gin.Context) {
	session := sessions.Default(ctx)
	userID, ok := session.Get(UserKey).(string)
	if !ok {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("用户不存在")))
		return
	}

	user, err := a.getAuthService().GetUserByID(userID)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("用户不存在")))
		return
	}

	token, expires, err := a.getAuthService().LoginWithJWT(user)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("生成Token失败")))
		return
	}

	ctx.JSON(http.StatusOK, api.Okay(api.WithData(gin.H{
		"token":   token,
		"expires": expires,
		"user":    user,
	})))
}

func (a *authHandler) Logout(ctx *gin.Context) {
	// 清除Session
	err := a.getAuthService().Logout(ctx)
	if err != nil {
		core.Logger().Errorf("登出失败: %v", err)
	}

	ctx.Redirect(http.StatusFound, passport.Logout(ctx.Query("redirect_uri")))
}

// RefreshToken 刷新JWT Token
func (a *authHandler) RefreshToken(ctx *gin.Context) {
	tokenString := ctx.GetHeader("Authorization")
	if tokenString == "" {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("缺少Authorization头")))
		return
	}

	// 移除 "Bearer " 前缀
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	newToken, expires, err := a.getAuthService().RefreshToken(tokenString)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("Token刷新失败: "+err.Error())))
		return
	}

	ctx.JSON(http.StatusOK, api.Okay(api.WithData(gin.H{
		"token":   newToken,
		"expires": expires,
	})))
}

// Me 获取当前用户信息
func (a *authHandler) Me(ctx *gin.Context) {
	// 从上下文中获取用户信息（由认证中间件设置）
	if user, exists := ctx.Get("ctx_user_key"); exists {
		if u, ok := user.(*entity.User); ok {
			ctx.JSON(http.StatusOK, api.Okay(api.WithData(u)))
			return
		}
	}

	ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("用户未登录")))
}

// LoginWithPassword 用户名密码登录（如果需要的话）
func (a *authHandler) LoginWithPassword(ctx *gin.Context) {
	var req struct {
		Email    string `json:"email" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, api.Fail(api.WithMessage("参数错误")))
		return
	}

	// 这里可以添加密码验证逻辑
	// 目前项目使用OAuth登录，这个方法可以根据需要实现
	ctx.JSON(http.StatusNotImplemented, api.Fail(api.WithMessage("暂不支持密码登录")))
}
