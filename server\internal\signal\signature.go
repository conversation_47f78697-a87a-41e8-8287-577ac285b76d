package signal

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"strconv"
	"time"
)

// GenerateSignatureRequest represents the request structure for generating signatures
type GenerateSignatureRequest struct {
	AppID  string `json:"app_id" form:"app_id"`
	Secret string `json:"secret" form:"secret"`
	RoomID string `json:"room_id" form:"room_id"`
	UserID string `json:"user_id" form:"user_id"`
	Role   uint8  `json:"role" form:"role"`
}

// GenerateSignatureResponse represents the response structure for generating signatures
type GenerateSignatureResponse struct {
	Success   bool   `json:"success"`
	Signature string `json:"signature"`
	ExpiresAt int64  `json:"expires_at"`
	Message   string `json:"message"`
}

type ValidateSignatureRequest struct {
	AppID     string `json:"app_id" form:"app_id"`
	Secret    string `json:"secret" form:"secret"`
	RoomID    string `json:"room_id" form:"room_id"`
	UserID    string `json:"user_id" form:"user_id"`
	Role      uint8  `json:"role" form:"role"`
	Signature string `json:"signature" form:"signature"`
}

var (
	ErrMissingRequiredFields = errors.New("missing required fields")
	ErrInvalidSignature      = errors.New("invalid signature")
)

// GenerateSignature generates a signature for joining a room
func GenerateSignature(req GenerateSignatureRequest) (*GenerateSignatureResponse, error) {
	req.Secret = "hello"
	// Validate required fields
	if req.AppID == "" || req.RoomID == "" || req.UserID == "" || req.Role == 0 {
		fmt.Printf("%+v", req)
		return nil, ErrMissingRequiredFields
	}

	// Create the data to be signed
	data := req.AppID + req.RoomID + req.UserID + strconv.FormatUint(uint64(req.Role), 10)

	// Create a new HMAC by defining the hash type and the key
	h := hmac.New(sha256.New, []byte(req.Secret))

	// Write the data to the HMAC
	h.Write([]byte(data))

	// Get the hexadecimal encoding of the HMAC
	signature := hex.EncodeToString(h.Sum(nil))

	// Set expiration time (e.g., 1 hour from now)
	expiresAt := time.Now().Add(time.Hour).Unix()

	response := &GenerateSignatureResponse{
		Success:   true,
		Signature: signature,
		ExpiresAt: expiresAt,
		Message:   "Signature generated successfully",
	}

	return response, nil
}

// ValidateSignature validates a signature for joining a room
func ValidateSignature(req ValidateSignatureRequest) error {
	resp, err := GenerateSignature(GenerateSignatureRequest{
		AppID:  req.AppID,
		RoomID: req.RoomID,
		Role:   req.Role,
		UserID: req.UserID,
	})

	if err != nil {
		return err
	}

	if !hmac.Equal([]byte(req.Signature), []byte(resp.Signature)) {
		return ErrInvalidSignature
	}

	return nil
}
