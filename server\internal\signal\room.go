package signal

import (
	"sync"
)

// Room maintains the set of active clients and broadcasts messages to the clients.
type Room struct {
	// Room ID
	ID string

	// Registered clients.
	clients map[string]*Client

	mutex sync.RWMutex

	// Inbound messages from the clients.
	Broadcast chan *Message

	// Register requests from the clients.
	Register chan *Client

	// unregister requests from clients.
	unregister chan *Client

	close chan struct{}
}

// new<PERSON><PERSON> creates a new room
func newRoom(id string) *Room {
	return &Room{
		ID:         id,
		Broadcast:  make(chan *Message, 100), // Buffered channel
		Register:   make(chan *Client),
		unregister: make(chan *Client),
		clients:    make(map[string]*Client),
		close:      make(chan struct{}),
	}
}

func (r *Room) FindClient(clientId string) *Client {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	targetClient, _ := r.clients[clientId]

	return targetClient
}
func (r *Room) AllClients() []string {
	var ids []string
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	for id := range r.clients {
		ids = append(ids, id)
	}

	return ids
}

// Run starts the room's main loop
func (r *Room) Run() {
	for {
		select {
		case client := <-r.Register:
			r.mutex.Lock()
			r.clients[client.ID] = client
			r.mutex.Unlock()
		case client := <-r.unregister:
			r.mutex.Lock()
			if _, ok := r.clients[client.ID]; ok {
				delete(r.clients, client.ID)
				//close(client.send)
			}
			r.mutex.Unlock()
		case msg := <-r.Broadcast:
			if msg.From == nil {
				continue
			}
			r.mutex.RLock()
			for _, c := range r.clients {
				// Don't send message back to sender
				if c.ID != msg.From.ID {
					c.Send(msg)
				}
			}
			r.mutex.RUnlock()
		case <-r.close:
			// todo 是否需要关闭通道
			close(r.Register)
			close(r.unregister)
			close(r.Broadcast)
			close(r.close)
			return
		}
	}
}

func (r *Room) RegisterClient(client *Client) {
	r.Register <- client
}

func (r *Room) UnregisterClient(client *Client) {
	r.unregister <- client
}

func (r *Room) Close() {
	r.close <- struct{}{}
}
