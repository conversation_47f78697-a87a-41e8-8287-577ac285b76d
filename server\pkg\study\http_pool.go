package main

import (
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"io/ioutil"
	"net/http"
	"sync"
	"time"
)

var Test = new(test)

type test struct{}

// Http gin.UseH2C 开启http2，使用http连接池
// 使用wireshark抓包
func (*test) Http(ctx *gin.Context) {
	client := &http.Client{
		Timeout: time.Second * 10,
		Transport: &http.Transport{
			MaxConnsPerHost: 1,
		},
	}
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go newRequest(&wg, client)
	}
}

func newRequest(wg *sync.WaitGroup, client *http.Client) {
	defer wg.Done()
	req, err := http.NewRequest("GET", "http://127.0.0.1:8989/test1", nil)
	if err != nil {
		log.Fatal(err)
	}
	res, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer res.Body.Close()
	_, err = ioutil.ReadAll(res.Body)
	if err != nil {
		log.Fatal(err)
	}
}
