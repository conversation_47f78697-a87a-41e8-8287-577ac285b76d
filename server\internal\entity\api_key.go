package entity

import (
	"gorm.io/gorm"
	"time"
)

// APIKey represents an API key for a user with quota management
type APIKey struct {
	ID        uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint           `gorm:"type:bigint(20) unsigned;not null" json:"user_id"`
	Name      string         `gorm:"type:varchar(255);not null" json:"name"`
	Key       string         `gorm:"type:varchar(64);uniqueIndex;not null" json:"key"`
	Quota     uint           `gorm:"type:bigint(20) unsigned;not null;default:0" json:"quota"`      // 总额度
	UsedQuota uint           `gorm:"type:bigint(20) unsigned;not null;default:0" json:"used_quota"` // 已使用额度
	ExpiresAt *time.Time     `gorm:"type:timestamp" json:"expires_at,omitempty"`                    // 过期时间
	IsActive  bool           `gorm:"type:bool;not null;default:true" json:"is_active"`              // 是否激活
	CreatedAt time.Time      `gorm:"type:timestamp" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:timestamp" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at,omitempty"`
	User      User           `gorm:"foreignKey:UserID" json:"user,omitempty"`
}
