package tests

import (
	"log"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"meeting/internal/webrtc"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// TestWebRTCWebSocket tests the WebRTC WebSocket endpoint
func TestWebRTCWebSocket(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new WebRTC hub
	hub := webrtc.NewHub()

	// Start the hub in a goroutine
	go hub.Run()

	// Create a test HTTP server with the WebSocket handler
	router := gin.New()
	router.GET("/ws", webrtc.ServeWebSocket(hub))
	server := httptest.NewServer(router)
	defer server.Close()

	// Convert http:// to ws:// for WebSocket connection
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"

	// Connect to the WebSocket server
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket server: %v", err)
	}
	defer conn.Close()

	// Send a discover message
	discoverMsg := `{"type":"discover"}`
	err = conn.WriteMessage(websocket.TextMessage, []byte(discoverMsg))
	if err != nil {
		t.Fatalf("Failed to send discover message: %v", err)
	}

	// Wait for response
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, message, err := conn.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read response: %v", err)
	}

	// Log the response
	log.Printf("Received message: %s", message)

	// Check if we received a response
	if len(message) == 0 {
		t.Error("Expected a response message, but received nothing")
	}

	// Test successful
	t.Log("WebRTC WebSocket test passed")
}
