// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pkg/protocol/proto/chat.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConnectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          int32                  `protobuf:"varint,1,opt,name=role,proto3" json:"role,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Age           int32                  `protobuf:"varint,3,opt,name=age,proto3" json:"age,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectRequest) Reset() {
	*x = ConnectRequest{}
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectRequest) ProtoMessage() {}

func (x *ConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectRequest.ProtoReflect.Descriptor instead.
func (*ConnectRequest) Descriptor() ([]byte, []int) {
	return file_pkg_protocol_proto_chat_proto_rawDescGZIP(), []int{0}
}

func (x *ConnectRequest) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *ConnectRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConnectRequest) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

type ConnectReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectReply) Reset() {
	*x = ConnectReply{}
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectReply) ProtoMessage() {}

func (x *ConnectReply) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectReply.ProtoReflect.Descriptor instead.
func (*ConnectReply) Descriptor() ([]byte, []int) {
	return file_pkg_protocol_proto_chat_proto_rawDescGZIP(), []int{1}
}

func (x *ConnectReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SendMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_pkg_protocol_proto_chat_proto_rawDescGZIP(), []int{2}
}

func (x *SendMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SendMessageReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageReply) Reset() {
	*x = SendMessageReply{}
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageReply) ProtoMessage() {}

func (x *SendMessageReply) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_protocol_proto_chat_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageReply.ProtoReflect.Descriptor instead.
func (*SendMessageReply) Descriptor() ([]byte, []int) {
	return file_pkg_protocol_proto_chat_proto_rawDescGZIP(), []int{3}
}

func (x *SendMessageReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

var File_pkg_protocol_proto_chat_proto protoreflect.FileDescriptor

const file_pkg_protocol_proto_chat_proto_rawDesc = "" +
	"\n" +
	"\x1dpkg/protocol/proto/chat.proto\"J\n" +
	"\x0eConnectRequest\x12\x12\n" +
	"\x04role\x18\x01 \x01(\x05R\x04role\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x10\n" +
	"\x03age\x18\x03 \x01(\x05R\x03age\"(\n" +
	"\fConnectReply\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\".\n" +
	"\x12SendMessageRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\",\n" +
	"\x10SendMessageReply\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent2l\n" +
	"\x04Chat\x12+\n" +
	"\aConnect\x12\x0f.ConnectRequest\x1a\r.ConnectReply\"\x00\x127\n" +
	"\vSendMessage\x12\x13.SendMessageRequest\x1a\x11.SendMessageReply\"\x00B\tZ\a./pb;pbb\x06proto3"

var (
	file_pkg_protocol_proto_chat_proto_rawDescOnce sync.Once
	file_pkg_protocol_proto_chat_proto_rawDescData []byte
)

func file_pkg_protocol_proto_chat_proto_rawDescGZIP() []byte {
	file_pkg_protocol_proto_chat_proto_rawDescOnce.Do(func() {
		file_pkg_protocol_proto_chat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_protocol_proto_chat_proto_rawDesc), len(file_pkg_protocol_proto_chat_proto_rawDesc)))
	})
	return file_pkg_protocol_proto_chat_proto_rawDescData
}

var file_pkg_protocol_proto_chat_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pkg_protocol_proto_chat_proto_goTypes = []any{
	(*ConnectRequest)(nil),     // 0: ConnectRequest
	(*ConnectReply)(nil),       // 1: ConnectReply
	(*SendMessageRequest)(nil), // 2: SendMessageRequest
	(*SendMessageReply)(nil),   // 3: SendMessageReply
}
var file_pkg_protocol_proto_chat_proto_depIdxs = []int32{
	0, // 0: Chat.Connect:input_type -> ConnectRequest
	2, // 1: Chat.SendMessage:input_type -> SendMessageRequest
	1, // 2: Chat.Connect:output_type -> ConnectReply
	3, // 3: Chat.SendMessage:output_type -> SendMessageReply
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pkg_protocol_proto_chat_proto_init() }
func file_pkg_protocol_proto_chat_proto_init() {
	if File_pkg_protocol_proto_chat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_protocol_proto_chat_proto_rawDesc), len(file_pkg_protocol_proto_chat_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pkg_protocol_proto_chat_proto_goTypes,
		DependencyIndexes: file_pkg_protocol_proto_chat_proto_depIdxs,
		MessageInfos:      file_pkg_protocol_proto_chat_proto_msgTypes,
	}.Build()
	File_pkg_protocol_proto_chat_proto = out.File
	file_pkg_protocol_proto_chat_proto_goTypes = nil
	file_pkg_protocol_proto_chat_proto_depIdxs = nil
}
