<template>
  <div v-if="showDebugPanel" class="debug-panel">
    <div class="debug-header">
      <h3>Video Debug Panel</h3>
      <div class="debug-controls">
        <button @click="refreshData" class="debug-btn">Refresh</button>
        <button @click="clearLogs" class="debug-btn">Clear Logs</button>
        <button @click="exportLogs" class="debug-btn">Export</button>
        <button @click="togglePanel" class="debug-btn close">×</button>
      </div>
    </div>
    
    <div class="debug-content">
      <!-- Summary Stats -->
      <div class="debug-section">
        <h4>Summary</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Total Logs:</span>
            <span class="stat-value">{{ summary.totalLogs }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Errors:</span>
            <span class="stat-value error">{{ summary.errorLogs }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Error Rate:</span>
            <span class="stat-value">{{ summary.errorRate }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Participants:</span>
            <span class="stat-value">{{ summary.participantCount }}</span>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="debug-section">
        <h4>Performance Metrics</h4>
        <div v-if="metrics.size > 0" class="metrics-list">
          <div v-for="[participantId, metric] in metrics" :key="participantId" class="metric-item">
            <div class="metric-header">{{ participantId }}</div>
            <div class="metric-details">
              <span>Assignment: {{ metric.streamAssignmentTime.toFixed(2) }}ms</span>
              <span>Load: {{ metric.videoLoadTime.toFixed(2) }}ms</span>
              <span>Retries: {{ metric.retryCount }}</span>
              <span>Errors: {{ metric.errorCount }}</span>
              <span>Success: {{ metric.successRate.toFixed(1) }}%</span>
            </div>
          </div>
        </div>
        <div v-else class="no-data">No performance data available</div>
      </div>

      <!-- Log Filters -->
      <div class="debug-section">
        <h4>Log Filters</h4>
        <div class="filter-controls">
          <select v-model="selectedLogLevel" @change="filterLogs">
            <option value="">All Levels</option>
            <option value="0">Debug</option>
            <option value="1">Info</option>
            <option value="2">Warn</option>
            <option value="3">Error</option>
          </select>
          <select v-model="selectedCategory" @change="filterLogs">
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category" :value="category">
              {{ category }}
            </option>
          </select>
          <input 
            v-model="participantFilter" 
            @input="filterLogs"
            placeholder="Filter by participant ID"
            class="participant-filter"
          />
        </div>
      </div>

      <!-- Recent Logs -->
      <div class="debug-section">
        <h4>Recent Logs ({{ filteredLogs.length }})</h4>
        <div class="logs-container">
          <div 
            v-for="log in recentLogs" 
            :key="`${log.timestamp}-${log.message}`"
            class="log-entry"
            :class="getLogLevelClass(log.level)"
          >
            <div class="log-header">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-level">{{ getLogLevelName(log.level) }}</span>
              <span class="log-category">{{ log.category }}</span>
              <span v-if="log.participantId" class="log-participant">{{ log.participantId.slice(0, 8) }}</span>
            </div>
            <div class="log-message">{{ log.message }}</div>
            <div v-if="log.data" class="log-data">
              <pre>{{ formatLogData(log.data) }}</pre>
            </div>
            <div v-if="log.duration" class="log-duration">
              Duration: {{ log.duration.toFixed(2) }}ms
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Debug Toggle Button -->
  <button 
    v-if="!showDebugPanel" 
    @click="togglePanel" 
    class="debug-toggle"
    title="Open Video Debug Panel"
  >
    🐛
  </button>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { videoLogger, LogLevel, type VideoLogEntry, type PerformanceMetrics } from '@/utils/videoLogger'

const showDebugPanel = ref(false)
const selectedLogLevel = ref('')
const selectedCategory = ref('')
const participantFilter = ref('')
const refreshInterval = ref<NodeJS.Timeout>()

// Reactive data
const summary = ref<any>({})
const metrics = ref<Map<string, PerformanceMetrics>>(new Map())
const allLogs = ref<VideoLogEntry[]>([])
const filteredLogs = ref<VideoLogEntry[]>([])

// Computed properties
const categories = computed(() => {
  const cats = new Set(allLogs.value.map(log => log.category))
  return Array.from(cats).sort()
})

const recentLogs = computed(() => {
  return filteredLogs.value.slice(-50).reverse() // Show last 50 logs, newest first
})

// Methods
function togglePanel() {
  showDebugPanel.value = !showDebugPanel.value
  if (showDebugPanel.value) {
    refreshData()
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

function refreshData() {
  summary.value = videoLogger.getSummary()
  metrics.value = videoLogger.getAllMetrics()
  allLogs.value = videoLogger.exportLogs()
  filterLogs()
}

function filterLogs() {
  let logs = allLogs.value

  if (selectedLogLevel.value !== '') {
    const level = parseInt(selectedLogLevel.value)
    logs = logs.filter(log => log.level >= level)
  }

  if (selectedCategory.value) {
    logs = logs.filter(log => log.category === selectedCategory.value)
  }

  if (participantFilter.value) {
    const filter = participantFilter.value.toLowerCase()
    logs = logs.filter(log => 
      log.participantId?.toLowerCase().includes(filter) ||
      log.message.toLowerCase().includes(filter)
    )
  }

  filteredLogs.value = logs
}

function clearLogs() {
  videoLogger.clearLogs()
  refreshData()
}

function exportLogs() {
  const logsJson = videoLogger.exportLogsAsJSON(
    selectedLogLevel.value ? parseInt(selectedLogLevel.value) : undefined,
    selectedCategory.value || undefined
  )
  
  const blob = new Blob([logsJson], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `video-debug-logs-${new Date().toISOString().slice(0, 19)}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

function startAutoRefresh() {
  refreshInterval.value = setInterval(refreshData, 2000) // Refresh every 2 seconds
}

function stopAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = undefined
  }
}

function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString()
}

function getLogLevelName(level: LogLevel): string {
  return LogLevel[level]
}

function getLogLevelClass(level: LogLevel): string {
  switch (level) {
    case LogLevel.DEBUG: return 'log-debug'
    case LogLevel.INFO: return 'log-info'
    case LogLevel.WARN: return 'log-warn'
    case LogLevel.ERROR: return 'log-error'
    default: return ''
  }
}

function formatLogData(data: any): string {
  if (typeof data === 'string') return data
  return JSON.stringify(data, null, 2)
}

// Lifecycle
onMounted(() => {
  // Enable debug panel in development or when debug flag is set
  if (import.meta.env.DEV || localStorage.getItem('video-debug') === 'true') {
    // Auto-show in development
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})

// Keyboard shortcut to toggle debug panel
function handleKeydown(event: KeyboardEvent) {
  if (event.ctrlKey && event.shiftKey && event.key === 'D') {
    event.preventDefault()
    togglePanel()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid #333;
  border-radius: 8px;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #1a1a1a;
  border-bottom: 1px solid #333;
}

.debug-header h3 {
  margin: 0;
  font-size: 14px;
  color: #00ff00;
}

.debug-controls {
  display: flex;
  gap: 5px;
}

.debug-btn {
  padding: 4px 8px;
  background: #333;
  border: 1px solid #555;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.debug-btn:hover {
  background: #444;
}

.debug-btn.close {
  background: #ff4444;
  font-weight: bold;
}

.debug-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.debug-section {
  margin-bottom: 15px;
  border-bottom: 1px solid #333;
  padding-bottom: 10px;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  color: #00ccff;
  font-size: 13px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
}

.stat-label {
  color: #ccc;
}

.stat-value {
  color: #00ff00;
}

.stat-value.error {
  color: #ff4444;
}

.metrics-list {
  max-height: 150px;
  overflow-y: auto;
}

.metric-item {
  margin-bottom: 8px;
  padding: 5px;
  background: #1a1a1a;
  border-radius: 4px;
}

.metric-header {
  color: #00ccff;
  font-weight: bold;
  margin-bottom: 3px;
}

.metric-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 10px;
}

.metric-details span {
  color: #ccc;
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-controls select,
.participant-filter {
  padding: 4px;
  background: #333;
  border: 1px solid #555;
  color: white;
  border-radius: 4px;
  font-size: 11px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-entry {
  margin-bottom: 8px;
  padding: 5px;
  border-left: 3px solid #333;
  background: #1a1a1a;
}

.log-entry.log-debug {
  border-left-color: #666;
}

.log-entry.log-info {
  border-left-color: #00ccff;
}

.log-entry.log-warn {
  border-left-color: #ffaa00;
}

.log-entry.log-error {
  border-left-color: #ff4444;
}

.log-header {
  display: flex;
  gap: 8px;
  margin-bottom: 3px;
  font-size: 10px;
}

.log-time {
  color: #888;
}

.log-level {
  color: #00ff00;
  font-weight: bold;
}

.log-category {
  color: #00ccff;
}

.log-participant {
  color: #ffaa00;
}

.log-message {
  color: white;
  margin-bottom: 3px;
}

.log-data {
  background: #0a0a0a;
  padding: 3px;
  border-radius: 3px;
  margin-bottom: 3px;
}

.log-data pre {
  margin: 0;
  color: #ccc;
  font-size: 10px;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-duration {
  color: #888;
  font-size: 10px;
}

.no-data {
  color: #888;
  font-style: italic;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #00ff00;
  color: white;
  font-size: 20px;
  cursor: pointer;
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-toggle:hover {
  background: rgba(0, 255, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .debug-panel {
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    top: 20px;
    left: 20px;
    right: 20px;
  }
}
</style>