import {
  MessageType,
  WebRTCEventType,
  type ChatMessage,
  type FileTransfer,
  type MediaState,
  type Participant,
  type PeerConnection,
  type SignalMessage
} from '@/types/webrtc'
import { iceServers } from '@/config'

export class WebRTCService {
  private ws: WebSocket | null = null
  private localStream: MediaStream | null = null
  private screenStream: MediaStream | null = null
  private peers: Map<string, PeerConnection> = new Map()
  private clientId: string
  private roomId: string
  private mediaState: MediaState = { video: false, audio: false, screen: false }

  // Event callbacks
  public onParticipantJoined?: (participant: Participant) => void
  public onParticipantLeft?: (participantId: string) => void
  public onRemoteStream?: (participantId: string, stream: MediaStream) => void
  public onChatMessage?: (message: ChatMessage) => void
  public onFileReceived?: (file: File) => void
  public onFileProgress?: (transfer: FileTransfer) => void
  public onMediaStateChanged?: (participantId: string, mediaState: MediaState) => void

  private fileTransfers: Map<string, FileTransfer> = new Map()
  private readonly CHUNK_SIZE = 16384 // 16KB chunks

  constructor(clientId: string, roomId: string) {
    this.clientId = clientId
    this.roomId = roomId
  }

  async connect(wsUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(`${wsUrl}?client_id=${this.clientId}&room_id=${this.roomId}`)

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.sendMessage({ type: MessageType.Join })
        resolve()
      }

      this.ws.onmessage = (event) => {
        // Handle multiple messages separated by newlines
        const messages = event.data.trim().split('\n')
        for (const messageData of messages) {
          if (messageData.trim()) {
            try {
              const message: SignalMessage = JSON.parse(messageData)
              this.handleSignalMessage(message)
            } catch (error) {
              console.error('Error parsing WebSocket message:', error, 'Raw data:', messageData)
            }
          }
        }
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        reject(error)
      }

      this.ws.onclose = () => {
        console.log('WebSocket disconnected')
        this.cleanup()
      }
    })
  }

  private async handleSignalMessage(message: SignalMessage): Promise<void> {
    const { type, from, data } = message
    console.log('Received signal message:', type, 'from:', from!.id, 'data:', data)

    switch (type) {
      case MessageType.Join:
        if (from!.id !== this.clientId) {
          console.log('Peer joined:', from!.id)
          await this.handlePeerJoined(from!.id)
        }
        break

      case MessageType.Leave:
        console.log('Peer left:', from!.id)
        this.handlePeerLeft(from!.id)
        break

      // todo 可能存在重复连接
      case MessageType.AllClients:
        if (Array.isArray(data)) {
          console.log('All clients:', data)
          for (const clientId of data) {
            if (clientId !== this.clientId) {
              await this.handlePeerJoined(clientId)
            }
          }
        }
        break

      case MessageType.WebRTCEvent:
        switch (data.type) {
          case WebRTCEventType.Offer:
            console.log('Received offer from:', from!.id)
            await this.handleOffer(from!.id, data.data)
            break
          case WebRTCEventType.Answer:
            console.log('Received answer from:', from!.id)
            await this.handleAnswer(from!.id, data.data)
            break
          case WebRTCEventType.IceCandidate:
            console.log('Received ICE candidate from:', from!.id)
            await this.handleIceCandidate(from!.id, data.data)
            break
        }
    }
  }

  private async handlePeerJoined(peerId: string): Promise<void> {
    const peerConnection = await this.createPeerConnection(peerId)

    // Tracks are already added in createPeerConnection, no need to add again

    // Only create offer if this client's ID is lexicographically smaller
    // This prevents both peers from creating offers simultaneously
    if (this.clientId != peerId) {
      const offer = await peerConnection.connection.createOffer()
      await peerConnection.connection.setLocalDescription(offer)

      this.sendMessage({
        type: MessageType.WebRTCEvent,
        to: { id: peerId },
        data: {
          type: WebRTCEventType.Offer,
          data: offer
        }
      })
    }

    this.onParticipantJoined?.({
      id: peerId,
      name: `User ${peerId.slice(0, 8)}`,
      mediaState: { video: false, audio: false, screen: false }
    })
  }

  private handlePeerLeft(peerId: string): void {
    const peer = this.peers.get(peerId)
    if (peer) {
      peer.connection.close()
      peer.dataChannel?.close()
      this.peers.delete(peerId)
      this.onParticipantLeft?.(peerId)
    }
  }

  private async handleOffer(peerId: string, offer: RTCSessionDescriptionInit): Promise<void> {
    let peerConnection = this.peers.get(peerId)

    if (!peerConnection) {
      peerConnection = await this.createPeerConnection(peerId)
    }

    // Check connection state before setting remote description
    if (
      peerConnection.connection.signalingState === 'stable' ||
      peerConnection.connection.signalingState === 'have-local-offer'
    ) {
      await peerConnection.connection.setRemoteDescription(offer)

      // Tracks are already added in createPeerConnection, no need to add again
      console.log(`Peer ${peerId} already has tracks from createPeerConnection`)

      const answer = await peerConnection.connection.createAnswer()
      await peerConnection.connection.setLocalDescription(answer)

      this.sendMessage({
        type: MessageType.WebRTCEvent,
        to: { id: peerId },
        data: {
          type: WebRTCEventType.Answer,
          data: answer
        }
      })
    } else {
      console.warn(
        `Cannot set remote description, connection state: ${peerConnection.connection.signalingState}`
      )
    }
  }

  private async handleAnswer(peerId: string, answer: RTCSessionDescriptionInit): Promise<void> {
    const peer = this.peers.get(peerId)
    if (peer) {
      // Check connection state before setting remote description
      if (peer.connection.signalingState === 'have-local-offer') {
        await peer.connection.setRemoteDescription(answer)
      } else {
        console.warn(
          `Cannot set remote answer, connection state: ${peer.connection.signalingState}`
        )
      }
    } else {
      console.warn(`Peer ${peerId} not found`)
    }
  }

  private async handleIceCandidate(peerId: string, candidate: RTCIceCandidateInit): Promise<void> {
    const peer = this.peers.get(peerId)
    if (peer && candidate) {
      try {
        // Only add ICE candidate if remote description is set
        if (peer.connection.remoteDescription) {
          await peer.connection.addIceCandidate(new RTCIceCandidate(candidate))
        } else {
          console.warn('Remote description not set yet, ignoring ICE candidate')
        }
      } catch (error) {
        console.error('Error adding ICE candidate:', error)
      }
    }
  }

  private async createPeerConnection(peerId: string): Promise<PeerConnection> {
    const configuration: RTCConfiguration = { iceServers }

    const connection = new RTCPeerConnection(configuration)

    // Handle ICE candidates
    connection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendMessage({
          type: MessageType.WebRTCEvent,
          to: { id: peerId },
          data: {
            type: WebRTCEventType.IceCandidate,
            data: event.candidate
          }
        })
      }
    }

    console.log('Creating peer connection for:', peerId)

    // Handle remote stream
    connection.ontrack = (event) => {
      const [remoteStream] = event.streams
      console.log('Received remote stream from:', peerId)
      this.onRemoteStream?.(peerId, remoteStream)

      const peer = this.peers.get(peerId)
      if (peer) {
        peer.remoteStream = remoteStream
      }
    }

    // Create data channel for the initiator
    const dataChannel = connection.createDataChannel('messages', {
      ordered: true
    })

    this.setupDataChannel(dataChannel, peerId)

    // Handle incoming data channels
    connection.ondatachannel = (event) => {
      this.setupDataChannel(event.channel, peerId)
    }

    // Add tracks from the appropriate stream
    const streamToUse = this.screenStream || this.localStream

    if (streamToUse) {
      streamToUse.getTracks().forEach((track) => {
        try {
          console.log(
            `Adding ${track.kind} track to new peer ${peerId} from ${this.screenStream ? 'screen' : 'camera'}`
          )
          connection.addTrack(track, streamToUse)
        } catch (error) {
          console.error(`Failed to add ${track.kind} track to new peer ${peerId}:`, error)
        }
      })
    }

    const peerConnection: PeerConnection = {
      id: peerId,
      connection,
      dataChannel
    }

    this.peers.set(peerId, peerConnection)
    return peerConnection
  }

  private setupDataChannel(dataChannel: RTCDataChannel, peerId: string): void {
    dataChannel.onopen = () => {
      console.log(`Data channel opened with ${peerId}`)
    }

    dataChannel.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleDataChannelMessage(data, peerId)
      } catch (error) {
        console.error('Error parsing data channel message:', error)
      }
    }

    dataChannel.onerror = (error) => {
      console.error(`Data channel error with ${peerId}:`, error)
    }

    // Update peer's data channel reference
    const peer = this.peers.get(peerId)
    if (peer) {
      peer.dataChannel = dataChannel
    }
  }

  private handleDataChannelMessage(data: any, senderId: string): void {
    switch (data.type) {
      case 'chat':
        this.onChatMessage?.({
          id: data.id,
          senderId,
          senderName: data.senderName,
          content: data.content,
          timestamp: data.timestamp,
          type: 'text'
        })
        break

      case 'file-start':
        this.handleFileTransferStart(data, senderId)
        break

      case 'file-chunk':
        this.handleFileChunk(data, senderId)
        break

      case 'file-end':
        this.handleFileTransferEnd(data, senderId)
        break

      case 'media-state':
        this.onMediaStateChanged?.(senderId, data.mediaState)
        break
    }
  }

  async startCamera(): Promise<MediaStream> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      })

      this.mediaState.video = true
      this.mediaState.audio = true
      this.broadcastMediaState()

      // If there are existing peers, renegotiate to add new tracks
      if (this.peers.size > 0) {
        console.log('Renegotiating connections to add camera tracks')
        await this.renegotiateAllConnections()
      }

      return this.localStream
    } catch (error) {
      console.error('Error accessing camera:', error)
      throw error
    }
  }

  async startScreenShare(): Promise<MediaStream> {
    try {
      this.screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      })

      this.mediaState.screen = true
      this.broadcastMediaState()

      // Renegotiate connections to switch to screen share
      if (this.peers.size > 0) {
        console.log('Renegotiating connections to switch to screen share')
        await this.renegotiateAllConnections()
      }

      // Handle screen share end
      this.screenStream.getVideoTracks()[0].onended = () => {
        this.stopScreenShare()
      }

      return this.screenStream
    } catch (error) {
      console.error('Error starting screen share:', error)
      throw error
    }
  }

  async stopScreenShare(): Promise<void> {
    if (this.screenStream) {
      this.screenStream.getTracks().forEach((track) => track.stop())
      this.screenStream = null
      this.mediaState.screen = false
      this.broadcastMediaState()

      // Renegotiate to switch back to camera or remove video
      if (this.peers.size > 0) {
        console.log('Renegotiating connections after stopping screen share')
        await this.renegotiateAllConnections()
      }
    }
  }

  toggleAudio(): boolean {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled
        this.mediaState.audio = audioTrack.enabled
        this.broadcastMediaState()
        return audioTrack.enabled
      }
    }
    return false
  }

  toggleVideo(): boolean {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled
        this.mediaState.video = videoTrack.enabled
        this.broadcastMediaState()
        return videoTrack.enabled
      }
    }
    return false
  }

  async stopCamera(): Promise<void> {
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => track.stop())
      this.localStream = null
      this.mediaState.video = false
      this.mediaState.audio = false
      this.broadcastMediaState()

      // Renegotiate to remove camera tracks
      if (this.peers.size > 0) {
        console.log('Renegotiating connections after stopping camera')
        await this.renegotiateAllConnections()
      }
    }
  }

  sendChatMessage(content: string): void {
    const message = {
      type: 'chat',
      id: Date.now().toString(),
      senderName: `User ${this.clientId.slice(0, 8)}`,
      content,
      timestamp: Date.now()
    }

    this.broadcastToDataChannels(message)

    // Add to own chat
    this.onChatMessage?.({
      ...message,
      senderId: this.clientId,
      type: 'text'
    })
  }

  async sendFile(file: File): Promise<void> {
    const fileId = Date.now().toString()
    const chunks: ArrayBuffer[] = []

    // Read file as array buffer
    const arrayBuffer = await file.arrayBuffer()

    // Split into chunks
    for (let i = 0; i < arrayBuffer.byteLength; i += this.CHUNK_SIZE) {
      chunks.push(arrayBuffer.slice(i, i + this.CHUNK_SIZE))
    }

    const transfer: FileTransfer = {
      id: fileId,
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: 'transferring',
      chunks: [],
      totalChunks: chunks.length
    }

    // Send file start message
    this.broadcastToDataChannels({
      type: 'file-start',
      fileId,
      name: file.name,
      size: file.size,
      mimeType: file.type,
      totalChunks: chunks.length
    })

    // Send chunks
    for (let i = 0; i < chunks.length; i++) {
      this.broadcastToDataChannels({
        type: 'file-chunk',
        fileId,
        chunkIndex: i,
        data: Array.from(new Uint8Array(chunks[i]))
      })

      transfer.progress = ((i + 1) / chunks.length) * 100
      this.onFileProgress?.(transfer)

      // Small delay to prevent overwhelming
      await new Promise((resolve) => setTimeout(resolve, 10))
    }

    // Send file end message
    this.broadcastToDataChannels({
      type: 'file-end',
      fileId
    })

    transfer.status = 'completed'
    this.onFileProgress?.(transfer)
  }

  private handleFileTransferStart(data: any, senderId: string): void {
    const transfer: FileTransfer = {
      id: data.fileId,
      name: data.name,
      size: data.size,
      type: data.mimeType,
      progress: 0,
      status: 'transferring',
      chunks: new Array(data.totalChunks),
      totalChunks: data.totalChunks
    }

    this.fileTransfers.set(data.fileId, transfer)
    this.onFileProgress?.(transfer)
  }

  private handleFileChunk(data: any, senderId: string): void {
    const transfer = this.fileTransfers.get(data.fileId)
    if (transfer) {
      transfer.chunks[data.chunkIndex] = new Uint8Array(data.data).buffer
      transfer.progress = ((data.chunkIndex + 1) / transfer.totalChunks) * 100
      this.onFileProgress?.(transfer)
    }
  }

  private handleFileTransferEnd(data: any, senderId: string): void {
    const transfer = this.fileTransfers.get(data.fileId)
    if (transfer) {
      // Combine all chunks
      const combinedBuffer = new ArrayBuffer(transfer.size)
      const combinedView = new Uint8Array(combinedBuffer)
      let offset = 0

      for (const chunk of transfer.chunks) {
        if (chunk) {
          combinedView.set(new Uint8Array(chunk), offset)
          offset += chunk.byteLength
        }
      }

      // Create file blob
      const blob = new Blob([combinedBuffer], { type: transfer.type })
      const file = new File([blob], transfer.name, { type: transfer.type })

      transfer.status = 'completed'
      this.onFileProgress?.(transfer)
      this.onFileReceived?.(file)

      this.fileTransfers.delete(data.fileId)
    }
  }

  private broadcastMediaState(): void {
    this.broadcastToDataChannels({
      type: 'media-state',
      mediaState: this.mediaState
    })
  }

  private broadcastToDataChannels(data: any): void {
    const message = JSON.stringify(data)
    this.peers.forEach((peer) => {
      if (peer.dataChannel && peer.dataChannel.readyState === 'open') {
        try {
          peer.dataChannel.send(message)
        } catch (error) {
          console.error(`Error sending to peer ${peer.id}:`, error)
        }
      }
    })
  }

  private async replaceVideoTrack(newTrack: MediaStreamTrack): Promise<void> {
    console.log('Replacing video track for', this.peers.size, 'peers')

    const promises: Promise<void>[] = []

    this.peers.forEach((peer, peerId) => {
      const senders = peer.connection.getSenders()
      console.log(`Peer ${peerId} has ${senders.length} senders`)

      const videoSender = senders.find((s) => s.track && s.track.kind === 'video')

      if (videoSender) {
        console.log(`Replacing video track for peer ${peerId}`)
        promises.push(
          videoSender.replaceTrack(newTrack).catch((error) => {
            console.error(`Failed to replace track for peer ${peerId}:`, error)
            // If replaceTrack fails, try renegotiation
            return this.renegotiateConnection(peerId)
          })
        )
      } else {
        console.log(`No video sender found for peer ${peerId}, need renegotiation`)
        promises.push(this.renegotiateConnection(peerId))
      }
    })

    await Promise.all(promises)
    console.log('Video track replacement completed')
  }

  private async renegotiateConnection(peerId: string): Promise<void> {
    const peer = this.peers.get(peerId)
    if (!peer) {
      console.warn(`Peer ${peerId} not found for renegotiation`)
      return
    }

    try {
      console.log(`Starting renegotiation with peer ${peerId}`)

      // Remove all existing tracks
      const senders = peer.connection.getSenders()
      for (const sender of senders) {
        if (sender.track) {
          peer.connection.removeTrack(sender)
        }
      }

      // Add current tracks
      const currentStream = this.screenStream || this.localStream
      if (currentStream) {
        currentStream.getTracks().forEach((track) => {
          console.log(`Adding ${track.kind} track during renegotiation with peer ${peerId}`)
          peer.connection.addTrack(track, currentStream)
        })
      }

      // Create new offer
      const offer = await peer.connection.createOffer()
      await peer.connection.setLocalDescription(offer)

      // Send offer to peer
      this.sendMessage({
        type: MessageType.WebRTCEvent,
        to: { id: peerId },
        data: {
          type: WebRTCEventType.Offer,
          data: offer
        }
      })

      console.log(`Renegotiation offer sent to peer ${peerId}`)
    } catch (error) {
      console.error(`Failed to renegotiate with peer ${peerId}:`, error)
    }
  }

  private async renegotiateAllConnections(): Promise<void> {
    console.log('Starting renegotiation for all peers')
    const promises: Promise<void>[] = []

    this.peers.forEach((_, peerId) => {
      promises.push(this.renegotiateConnection(peerId))
    })

    await Promise.all(promises)
    console.log('Renegotiation completed for all peers')
  }

  private sendMessage(message: SignalMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    }
  }

  private cleanup(): void {
    this.peers.forEach((peer) => {
      peer.connection.close()
      peer.dataChannel?.close()
    })
    this.peers.clear()

    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => track.stop())
    }

    if (this.screenStream) {
      this.screenStream.getTracks().forEach((track) => track.stop())
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.sendMessage({ type: MessageType.Leave })
      this.ws.close()
    }
    this.cleanup()
  }
}
