package middleware

import (
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"meeting/pkg/api"
	"meeting/pkg/auth"
	"meeting/pkg/jwt"
	"net/http"
)

// AuthenticationOptional 可选认证中间件，不强制要求用户登录
func AuthenticationOptional() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 尝试从JWT或Session中获取用户信息，但不强制要求
		ctx.Next()
	}
}

// Authentication 统一认证中间件，支持JWT和Session两种方式
func Authentication() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		user := auth.User(ctx)
		if user == nil {
			ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("认证失败")))
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}

// JWT JWT认证中间件，从Token中解析用户信息
func JWT() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		tokenString := jwt.ParseToken(ctx)
		if tokenString != "" {
			user, err := jwt.Authenticate(tokenString)
			if err == nil && user != nil {
				auth.Login(ctx, user)
			}
		}
		ctx.Next()
	}
}

// SessionAuth Session认证中间件，从Session中获取用户信息
func SessionAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		session := sessions.Default(ctx)
		if userID, ok := session.Get("user").(string); ok && userID != "" {
			// 从数据库获取用户信息
			user, err := auth.GetUserByID(userID)
			if err == nil && user != nil {
				auth.Login(ctx, user)
			}
		}
		ctx.Next()
	}
}
