//go:build wireinject
// +build wireinject

package main

import (
	"fmt"
	"meeting/api/router"
	"meeting/pkg/config"
	"net/http"

	"github.com/google/wire"
)

// Application 应用程序结构体
type Application struct {
	Server *http.Server
	Config *config.TomlConfig
}

// NewApplication 创建应用程序实例
func NewApplication(server *http.Server, config *config.TomlConfig) *Application {
	return &Application{
		Server: server,
		Config: config,
	}
}

// NewHTTPServer 创建HTTP服务器
func NewHTTPServer(cfg *config.TomlConfig) *http.Server {
	host := cfg.App.Host
	if host == "" {
		host = "0.0.0.0"
	}
	port := cfg.App.Port
	if port == 0 {
		port = 8989
	}

	return &http.Server{
		Addr:    fmt.Sprintf("%s:%d", host, port),
		Handler: router.NewRouter(),
	}
}

// InitializeApplication 初始化整个应用程序
func InitializeApplication(configPath string) (*Application, error) {
	wire.Build(
		// 基础设施层
		config.NewConfig,

		// HTTP层
		NewHTTPServer,

		// 应用程序
		NewApplication,
	)
	return &Application{}, nil
}
