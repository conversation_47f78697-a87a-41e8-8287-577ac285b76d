export enum MessageType {
  Join = 'join',
  Leave = 'leave',
  AllClients = 'all-clients',
  WebRTCEvent = 'webrtc-event'
}

export enum WebRTCEventType {
  Offer = 'offer',
  Answer = 'answer',
  IceCandidate = 'ice-candidate'
}

export interface SignalMessage {
  type: MessageType
  from?: {
    id: string
  }
  to?: {
    id: string
  }
  data?: any
}

export interface PeerConnection {
  id: string
  connection: RTCPeerConnection
  dataChannel?: RTCDataChannel
  remoteStream?: MediaStream
}

export interface ChatMessage {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: number
  type: 'text' | 'file'
  read?: boolean
}

export interface FileTransfer {
  id: string
  name: string
  size: number
  type: string
  progress: number
  status: 'pending' | 'transferring' | 'completed' | 'failed'
  chunks: ArrayBuffer[]
  totalChunks: number
}

export interface MediaState {
  video: boolean
  audio: boolean
  screen: boolean
}

export interface Participant {
  id: string
  name: string
  mediaState: MediaState
  stream?: MediaStream
}
