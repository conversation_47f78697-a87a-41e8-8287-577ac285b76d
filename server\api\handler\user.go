package handler

import (
	"github.com/gin-gonic/gin"
	"meeting/pkg/api"
	"meeting/pkg/config"
	"meeting/pkg/utils"
	"net/http"
	"os"
	"path"
	"time"
)

type user struct{}

var User = new(user)

func (*user) Upload(ctx *gin.Context) {
	file, err := ctx.FormFile("file")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, err)
		return
	}

	ud := config.GetConfig().App.UploadDir
	relativeDir := time.Now().Format("/200601/02/")
	absoluteDir := ud + relativeDir
	_, err = os.Stat(absoluteDir)
	if os.IsNotExist(err) {
		_ = os.MkdirAll(absoluteDir, os.FileMode(0755))
	}
	fileName := utils.GenerateRandomString(32)
	fileName = relativeDir + fileName + path.Ext(file.Filename)
	err = ctx.SaveUploadedFile(file, ud+fileName)
	if err != nil {
		ctx.JSON(http.StatusOK, api.Fail(api.WithMessage("上传失败："+err.Error())))
		return
	}
	ctx.JSON(http.StatusOK, api.Okay(api.WithData(gin.H{
		"url": "/upload" + fileName,
	})))
}

//func (*user) Signature(ctx *gin.Context) {
//	u, appID := authUtil.User(ctx), ctx.Query("AppID")
//	var app *entity.App
//	if tx := core.DB(context.Background()).Where("id", appID).Find(&app); tx.Error != nil || app.ID == "" {
//		ctx.JSON(http.StatusBadRequest, api.Fail(api.WithMessage("appID错误")))
//		return
//	}
//	fmt.Println("app", app)
//	p := &signature.Parameters{
//		AppID:   appID,
//		UserID:  u.ID,
//		Role:    1,
//		Expires: time.Now().Add(time.Hour * 60),
//	}
//	sig, err := signature.Sign(p, app.SecretKey)
//	if err != nil {
//		ctx.JSON(http.StatusBadRequest, api.Fail(api.WithMessage(err.Error())))
//		return
//	}
//
//	ctx.JSON(http.StatusOK, api.Okay(api.WithData(gin.H{
//		"parameters": p,
//		"signature":  sig,
//	})))
//}
