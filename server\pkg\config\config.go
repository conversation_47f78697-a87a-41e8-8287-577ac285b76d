package config

import (
	"github.com/BurntSushi/toml"
	"github.com/google/wire"
)

type TomlConfig struct {
	App struct {
		URL           string
		Host          string
		Port          uint16
		PIDFile       string
		LogFile       string
		UploadDir     string
		JWTSecret     string
		SessionSecret string
		Secure        bool
	}
	Im struct {
		AppID     string
		SecretKey string
	}
	Mysql struct {
		DSN string
	}
	Passport struct {
		URL          string
		ClientID     string
		ClientSecret string
		RedirectURI  string
		ResponseType string
		Scope        []string
		GrantType    string
	}
}

// NewConfig 创建配置实例
func NewConfig(filepath string) (*TomlConfig, error) {
	var c TomlConfig
	if _, err := toml.DecodeFile(filepath, &c); err != nil {
		return nil, err
	}
	return &c, nil
}

// ProviderSet Wire 提供者集合
var ProviderSet = wire.NewSet(NewConfig)

// 保持向后兼容的全局函数
var globalConfig *TomlConfig

func InitializeConfig(filepath string) {
	var err error
	globalConfig, err = NewConfig(filepath)
	if err != nil {
		panic(err)
	}
}

func GetConfig() TomlConfig {
	if globalConfig == nil {
		panic("config not initialized")
	}
	return *globalConfig
}

// SetGlobalConfig 设置全局配置
func SetGlobalConfig(cfg *TomlConfig) {
	globalConfig = cfg
}
