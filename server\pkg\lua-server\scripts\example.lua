-- 示例：WebSocket服务器Lua脚本

-- 连接建立时执行
function on_connect()
    server.log("客户端已连接: " .. server.get_client_id())
    
    -- 发送欢迎消息
    server.send_message("system", "欢迎使用WebSocket服务器！")
    
    -- 定时发送消息
    timer_id = server.set_interval(5, function()
        server.send_message("time", os.date("%Y-%m-%d %H:%M:%S"))
    end)
end

-- 消息处理
function on_message(msg)
    server.log("收到消息: " .. msg)
    
    -- 简单命令处理
    if msg == "ping" then
        server.send_message("pong", "PONG!")
    elseif msg == "time" then
        server.send_message("time", os.date("%Y-%m-%d %H:%M:%S"))
    elseif string.sub(msg, 1, 4) == "http" then
        -- 执行HTTP请求
        local body, err = server.http_get(msg)
        if body then
            server.send_message("http_response", body)
        else
            server.send_message("error", err)
        end
    end
end

-- 连接断开时执行
function on_disconnect()
    server.log("客户端已断开: " .. server.get_client_id())
    if timer_id then
        server.clear_interval(timer_id)
    end
end

-- 初始化
on_connect()    