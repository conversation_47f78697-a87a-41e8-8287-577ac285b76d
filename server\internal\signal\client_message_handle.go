package signal

import (
	"log"
)

// handleMessage processes messages from clients
func (c *Client) handleMessage(message *Message) {
	defer func() {
		if err := recover(); err != nil {
			log.Printf("Error processing message from client %s: %v", c.ID, err)
		}
	}()
	switch message.Type {
	case MessageTypeJoin:
		c.handleJoin(message)
	case MessageTypeLeave:
		c.handleLeave(message)
	case MessageTypeWebRTCEvent:
		c.handleWebRTCEvent(message)
	case MessageTypeAllClients:
		c.handleAllClients(message)
	case MessageTypeChat:
		c.handleChat(message)
	default:
		log.Printf("Unknown message type received from client %s: %s", c.ID, message.Type)
	}
}

func (c *Client) handleJoin(message *Message) {
	// Send all existing clients to the new client (excluding self)
	allClients := c.room.AllClients()
	var otherClients []string
	for _, clientId := range allClients {
		if clientId != c.ID {
			otherClients = append(otherClients, clientId)
		}
	}
	c.Send(c.newMessage(MessageTypeAllClients, otherClients, nil))

	// Broadcast join message to all other clients
	joinMsg := c.newMessage(MessageTypeJoin, nil, nil)
	c.room.Broadcast <- joinMsg
}

func (c *Client) handleLeave(message *Message) {
	// Broadcast leave message to all other clients
	leaveMsg := c.newMessage(MessageTypeLeave, nil, nil)
	c.room.Broadcast <- leaveMsg

	// Unregister the client
	c.room.UnregisterClient(c)
}

func (c *Client) handleChat(message *Message) {
	c.room.Broadcast <- c.newMessage(MessageTypeChat, message.Data, nil)
}

func (c *Client) handleAllClients(message *Message) {
	c.Send(c.newMessage(MessageTypeAllClients, c.room.AllClients(), nil))
}

func (c *Client) handleWebRTCEvent(message *Message) {
	targetClient := c.room.FindClient(message.To.ID)
	if targetClient == nil {
		log.Printf("Target client %s not found for offer", message.To.ID)
		return
	}

	targetClient.Send(message)
}
