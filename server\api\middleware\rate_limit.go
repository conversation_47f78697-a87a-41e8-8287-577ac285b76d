package middleware

import (
	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
	"net/http"
)

func RateLimit(r rate.Limit, b int) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// limit := rate.Every(100 * time.Millisecond) // 每100ms向桶中放一个token
		limiter := rate.NewLimiter(r, b)
		if !limiter.Allow() {
			ctx.String(http.StatusTooManyRequests, "Too many requests")
			ctx.Abort()
			return
		}
		ctx.Next()
	}
}
