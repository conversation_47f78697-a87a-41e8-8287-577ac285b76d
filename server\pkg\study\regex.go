package main

import (
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"
)

func main() {
	fmt.Println(regexp.MatchString("H(.*)d!", "Hello, world!"))
	fmt.Println(regexp.Match(`H(.*)d!`, []byte("Hello, world!")))
	r := regexp.MustCompile(`(\w+)@(\w+)`)
	fmt.Println(r.ReplaceAllString("9@11@qq@rr", "Hello"))
	bytes, err := os.ReadFile("./t.md")
	fmt.Println(string(bytes))
	if err != nil {
		panic(err)
	}
	reg := regexp.MustCompile("\\[!\\[.*\\]\\((.*)\\)\\]\\((.*)\\)")
	subMatches := reg.FindAllSubmatch(bytes, 1000)
	result := string(bytes)
	for _, val := range subMatches {
		url := string(val[2])
		splited := strings.Split(url, "/")
		filename := splited[len(splited)-1]
		log.Println(url)
		result = strings.ReplaceAll(result, url, "images/"+filename)

		// resp, err := http.Get(url)
		// if err != nil {
		// 	log.Println(err)
		// 	continue
		// }
		// filename := splited[len(splited)-1]
		// file, err := os.Create("./images/" + filename)
		// if err != nil {
		// 	log.Println(err)
		// 	continue
		// }
		// _, err = io.Copy(file, resp.Body)
		// if err != nil {
		// 	log.Panicln(err)
		// 	continue
		// }
	}
	f, _ := os.Create("t1.md")
	f.WriteString(result)
}
