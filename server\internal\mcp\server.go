package mcp

import (
	"context"
	"github.com/modelcontextprotocol/go-sdk/mcp"
	"meeting/internal/mcp/tools/amap"
	"meeting/internal/mcp/tools/core"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/mark3labs/mcp-go/server"
)

// Server MCP服务器实例
type Server struct {
	server *server.MCPServer
	config *Config
}

type Input struct {
	Name string `json:"name" jsonschema:"the name of the person to greet"`
}

type Output struct {
	Greeting string `json:"greeting" jsonschema:"the greeting to tell to the user"`
}

func SayHi(ctx context.Context, req *mcp.CallToolRequest, input Input) (*mcp.CallToolResult, Output, error) {
	return nil, Output{Greeting: "Hi " + input.Name}, nil
}

// NewServer 创建新的MCP服务器
func NewServer(config *Config) *Server {
	if config == nil {
		config = DefaultConfig()
	}

	//server := mcp.NewServer(&mcp.Implementation{Name: "greeter", Version: "v1.0.0"}, nil)
	//mcp.AddTool(server, &mcp.Tool{Name: "greet", Description: "say hi"}, SayHi)
	//if err := server.Run(context.Background(), &mcp.StdioTransport{}); err != nil {
	//	log.Fatal(err)
	//}
	// 创建MCP服务器
	s := server.NewMCPServer(
		"Meeting MCP Server",
		"1.0.0",
		server.WithResourceCapabilities(true, true),
		server.WithPromptCapabilities(true),
		server.WithToolCapabilities(true),
		server.WithRecovery(),
	)

	// 添加工具
	s.AddTool(amap.GeoCode())
	s.AddTool(core.Name())

	return &Server{
		server: s,
		config: config,
	}
}

// GetHTTPHandler 获取HTTP处理器，用于集成到Gin路由中
func (m *Server) GetHTTPHandler() http.Handler {
	return server.NewStreamableHTTPServer(m.server)
}

// RegisterRoutes 注册MCP路由到Gin引擎
func (m *Server) RegisterRoutes(r *gin.Engine) {
	if !m.config.Enabled {
		return
	}

	// 将MCP服务器作为路由处理器
	mcpHandler := m.GetHTTPHandler()

	// 注册MCP路由，支持所有HTTP方法
	r.Any("/mcp/*path", gin.WrapH(mcpHandler))
	r.Any("/mcp", gin.WrapH(mcpHandler))
}

// StartStdioServer 启动标准输入输出服务器
func (m *Server) StartStdioServer() error {
	return server.ServeStdio(m.server)
}
