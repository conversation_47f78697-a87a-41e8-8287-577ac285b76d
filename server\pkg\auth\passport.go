package auth

import (
	"bytes"
	"encoding/json"
	"io"
	"meeting/pkg/api"
	"meeting/pkg/config"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type OAuthToken struct {
	TokenType    string `json:"token_type,omitempty"`
	ExpiresIn    int64  `json:"expires_in,omitempty"`
	AccessToken  string `json:"access_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
}

type UserInfo struct {
	ID              string
	Name            string
	Gender          uint8
	Email           string
	EmailVerifiedAt time.Time
	Avatar          string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

type PassportProvider struct {
}

func NewPassportProvider() *PassportProvider {
	return &PassportProvider{}
}

func (p *PassportProvider) Redirect() string {
	c := config.GetConfig()
	u := url.Values{}
	u.Add("client_id", c.Passport.ClientID)
	u.Add("redirect_uri", c.Passport.RedirectURI)
	u.Add("response_type", c.Passport.ResponseType)
	u.Add("scope", strings.Join(c.Passport.Scope, ","))

	return c.Passport.URL + "/oauth/authorize?" + u.Encode()
}

func (p *PassportProvider) RedirectLogout(redirectURI string) string {
	return config.GetConfig().Passport.URL + "/logout?redirect_uri=" + url.QueryEscape(redirectURI)
}

func (p *PassportProvider) CodeToToken(code string) *OAuthToken {
	client := http.DefaultClient
	form := url.Values{}
	c := config.GetConfig()
	form.Add("grant_type", c.Passport.GrantType)
	form.Add("client_id", c.Passport.ClientID)
	form.Add("client_secret", c.Passport.ClientSecret)
	form.Add("redirect_uri", c.Passport.RedirectURI)
	form.Add("code", code)

	req, _ := http.NewRequest(http.MethodPost, c.Passport.URL+"/oauth/token", bytes.NewBufferString(form.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	response, err := client.Do(req)
	defer response.Body.Close()
	if err != nil {
		panic(err)
	}
	data, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	var t *OAuthToken
	err = json.Unmarshal(data, &t)
	if err != nil {
		panic(err)
	}

	return t
}

func (t *OAuthToken) GetUserInfo() *UserInfo {
	req, _ := http.NewRequest(http.MethodGet, config.GetConfig().Passport.URL+"/api/v1/user/profile", nil)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", "Bearer "+t.AccessToken)

	res, _ := http.DefaultClient.Do(req)
	defer res.Body.Close()
	data, _ := io.ReadAll(res.Body)
	var response api.Response[*UserInfo]
	json.Unmarshal(data, &response)

	return response.Data
}
