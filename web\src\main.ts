import './assets/main.css'
import adapter from 'webrtc-adapter'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import ToastService from 'primevue/toastservice'
import PrimeVue from 'primevue/config'
import Aura from '@primevue/themes/aura'
import 'primeicons/primeicons.css'
import Tooltip from 'primevue/tooltip'
import i18n from './plugins/i18n'

// if (import.meta.env.MODE !== 'development') {
//   try {
//     setInterval(() => {
//       ;(function () {
//         return false
//       })
//         ['constructor']('debugger')
//         ['call']()
//     }, 50)
//   } catch (e) {}
// }

console.log(adapter.browserDetails.browser)

const app = createApp(App)

app.use(createPinia())
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      cssLayer: {
        name: 'primevue',
        order: 'tailwind-base, primevue, tailwind-utilities'
      }
    }
  }
})
app.use(ToastService)
app.use(router)
app.use(i18n)
app.directive('tooltip', Tooltip)

app.mount('#app')