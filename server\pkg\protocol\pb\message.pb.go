// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pkg/protocol/proto/message.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Message struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Opcode        int32                  `protobuf:"varint,2,opt,name=opcode,proto3" json:"opcode,omitempty"`
	Payload       string                 `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	Timestamp     string                 `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_pkg_protocol_proto_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_protocol_proto_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_pkg_protocol_proto_message_proto_rawDescGZIP(), []int{0}
}

func (x *Message) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Message) GetOpcode() int32 {
	if x != nil {
		return x.Opcode
	}
	return 0
}

func (x *Message) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *Message) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

var File_pkg_protocol_proto_message_proto protoreflect.FileDescriptor

const file_pkg_protocol_proto_message_proto_rawDesc = "" +
	"\n" +
	" pkg/protocol/proto/message.proto\"i\n" +
	"\aMessage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06opcode\x18\x02 \x01(\x05R\x06opcode\x12\x18\n" +
	"\apayload\x18\x03 \x01(\tR\apayload\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\tR\ttimestampB\tZ\a./pb;pbb\x06proto3"

var (
	file_pkg_protocol_proto_message_proto_rawDescOnce sync.Once
	file_pkg_protocol_proto_message_proto_rawDescData []byte
)

func file_pkg_protocol_proto_message_proto_rawDescGZIP() []byte {
	file_pkg_protocol_proto_message_proto_rawDescOnce.Do(func() {
		file_pkg_protocol_proto_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_protocol_proto_message_proto_rawDesc), len(file_pkg_protocol_proto_message_proto_rawDesc)))
	})
	return file_pkg_protocol_proto_message_proto_rawDescData
}

var file_pkg_protocol_proto_message_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pkg_protocol_proto_message_proto_goTypes = []any{
	(*Message)(nil), // 0: Message
}
var file_pkg_protocol_proto_message_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pkg_protocol_proto_message_proto_init() }
func file_pkg_protocol_proto_message_proto_init() {
	if File_pkg_protocol_proto_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_protocol_proto_message_proto_rawDesc), len(file_pkg_protocol_proto_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pkg_protocol_proto_message_proto_goTypes,
		DependencyIndexes: file_pkg_protocol_proto_message_proto_depIdxs,
		MessageInfos:      file_pkg_protocol_proto_message_proto_msgTypes,
	}.Build()
	File_pkg_protocol_proto_message_proto = out.File
	file_pkg_protocol_proto_message_proto_goTypes = nil
	file_pkg_protocol_proto_message_proto_depIdxs = nil
}
