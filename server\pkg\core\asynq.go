package core

import (
	"context"
	"github.com/hibiken/asynq"
	"sync"
)

var (
	queueOnce   sync.Once
	queueClient *asynq.Client
)

func InitializeQueue() {
	queueOnce.Do(func() {
		rdbOpt := asynq.RedisClientOpt{
			Addr:     "localhost:6379",
			PoolSize: 10,
			DB:       0,
		}
		srv := asynq.NewServer(rdbOpt, asynq.Config{Concurrency: 5})
		if err := srv.Start(asynq.HandlerFunc(func(ctx context.Context, task *asynq.Task) error {
			return nil
		})); err != nil {
			panic(err)
		}
		queueClient = asynq.NewClient(rdbOpt)
	})
}

func Queue() *asynq.Client {
	return queueClient
}
