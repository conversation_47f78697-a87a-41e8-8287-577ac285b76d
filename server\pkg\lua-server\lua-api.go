package main

import (
	"encoding/json"
	"io/ioutil"
	"log"
	"net/http"
	"time"

	lua "github.com/yuin/gopher-lua"
)

// 注册Lua API
func RegisterLuaAPI(L *lua.LState) {
	// 创建模块
	mod := L.SetFuncs(L.<PERSON>(), map[string]lua.LGFunction{
		"send_message":  SendMessageAPI,
		"get_client_id": GetClientIDAPI,
		"log":           LogAPI,
		"http_get":      HTTPGetAPI,
		"sleep":         SleepAPI,
	})

	// 将模块设置为全局变量
	L.SetGlobal("server", mod)
}

// 发送消息API
func SendMessageAPI(L *lua.LState) int {
	// 获取客户端
	client := getClientFromLuaState(L)
	if client == nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("客户端未找到"))
		return 2
	}

	// 获取参数
	msgType := L.CheckString(1)
	data := L.CheckAny(2)

	// 构建消息
	msg := Message{
		Type: msgType,
		Data: data,
	}

	// 序列化消息
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	// 发送消息
	client.Send <- msgBytes

	L.Push(lua.LBool(true))
	return 1
}

// 获取客户端ID API
func GetClientIDAPI(L *lua.LState) int {
	client := getClientFromLuaState(L)
	if client == nil {
		L.Push(lua.LNil)
		return 1
	}

	L.Push(lua.LString(client.ID))
	return 1
}

// 日志API
func LogAPI(L *lua.LState) int {
	message := L.CheckString(1)
	log.Println("[Lua] " + message)
	return 0
}

// HTTP GET请求API
func HTTPGetAPI(L *lua.LState) int {
	url := L.CheckString(1)

	resp, err := http.Get(url)
	if err != nil {
		L.Push(lua.LNil)
		L.Push(lua.LString(err.Error()))
		return 2
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		L.Push(lua.LNil)
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LString(string(body)))
	return 1
}

// 睡眠API
func SleepAPI(L *lua.LState) int {
	seconds := L.CheckNumber(1)
	time.Sleep(time.Duration(seconds) * time.Second)
	return 0
}

// 从Lua状态中获取客户端
func getClientFromLuaState(L *lua.LState) *Client {
	ud := L.Get(L.GetTop()).(*lua.LUserData)
	if client, ok := ud.Value.(*Client); ok {
		return client
	}
	return nil
}
