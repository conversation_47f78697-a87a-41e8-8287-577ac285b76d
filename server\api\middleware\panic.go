package middleware

import (
	"meeting/pkg/api"
	"meeting/pkg/core"
	"net/http"

	"github.com/gin-gonic/gin"
)

type middleware struct{}

var Middleware = new(middleware)

func (*middleware) Panic() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				switch err.(type) {
				case error:
					ctx.JSON(http.StatusOK, api.Fail(api.WithMessage(err.(error).Error())))
				case string:
					ctx.JSON(http.StatusOK, api.Fail(api.WithMessage(err.(string))))
				}
				core.Logger().Infof("%s\n", err)
				ctx.Abort()
			}
		}()

		ctx.Next()
	}
}
