<template>
  <div class="flex justify-between items-center px-6 py-4 bg-black/80 backdrop-blur-lg border-t border-white/10">
    <div class="flex gap-3 items-center">
      <Button
        :icon="currentUser?.mediaState.audio ? 'pi pi-microphone' : 'pi pi-microphone-slash'"
        :severity="!currentUser?.mediaState.audio ? 'danger' : undefined"
        @click="toggleAudio"
        v-tooltip.top="currentUser?.mediaState.audio ? 'Mute' : 'Unmute'"
        rounded
        class="w-12 h-12"
      />
      
      <Button
        :icon="currentUser?.mediaState.video ? 'pi pi-video' : 'pi pi-video-slash'"
        :severity="!currentUser?.mediaState.video ? 'danger' : undefined"
        @click="toggleVideo"
        v-tooltip.top="currentUser?.mediaState.video ? 'Stop Video' : 'Start Video'"
        rounded
        class="w-12 h-12"
      />
      
      <Button
        :icon="currentUser?.mediaState.screen ? 'pi pi-stop' : 'pi pi-desktop'"
        :severity="currentUser?.mediaState.screen ? 'success' : undefined"
        @click="toggleScreenShare"
        v-tooltip.top="currentUser?.mediaState.screen ? 'Stop Sharing' : 'Share Screen'"
        rounded
        class="w-12 h-12"
      />
    </div>

    <div class="flex-1 flex justify-center">
      <div class="flex flex-col items-center gap-1 text-white">
        <span class="font-semibold text-sm">Room: {{ roomId }}</span>
        <span class="text-xs opacity-80">{{ participantsList.length }} participants</span>
      </div>
    </div>

    <div class="flex gap-3 items-center">
      <Button
        icon="pi pi-comments"
        :badge="unreadMessages > 0 ? unreadMessages.toString() : undefined"
        @click="toggleChat"
        v-tooltip.top="'Chat'"
        rounded
        outlined
        class="w-12 h-12"
      />
      
      <input
        ref="fileInputRef"
        type="file"
        multiple
        @change="handleFileSelect"
        class="hidden"
      />
      
      <Button
        icon="pi pi-paperclip"
        @click="() => fileInputRef?.click()"
        v-tooltip.top="'Send File'"
        rounded
        outlined
        class="w-12 h-12"
      />
      
      <Button
        icon="pi pi-sign-out"
        severity="danger"
        @click="leaveMeeting"
        v-tooltip.top="'Leave Meeting'"
        rounded
        class="w-12 h-12"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import Button from 'primevue/button'
import { useMeetingStore } from '@/stores/meeting'
import { useToast } from 'primevue/usetoast'

const emit = defineEmits<{
  toggleChat: []
}>()

const meetingStore = useMeetingStore()
const router = useRouter()
const toast = useToast()

const fileInputRef = ref<HTMLInputElement>()

const currentUser = computed(() => meetingStore.currentUser)
const participantsList = computed(() => meetingStore.participantsList)
const unreadMessages = computed(() => meetingStore.unreadMessages)
const roomId = computed(() => meetingStore.roomId)

async function toggleAudio() {
  try {
    const enabled = meetingStore.toggleAudio()
    toast.add({
      severity: enabled ? 'success' : 'info',
      summary: enabled ? 'Microphone On' : 'Microphone Off',
      life: 2000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Audio Error',
      detail: 'Failed to toggle microphone',
      life: 3000
    })
  }
}

async function toggleVideo() {
  try {
    if (!meetingStore.localStream) {
      await meetingStore.startCamera()
      toast.add({
        severity: 'success',
        summary: 'Camera Started',
        life: 2000
      })
    } else {
      const enabled = meetingStore.toggleVideo()
      toast.add({
        severity: enabled ? 'success' : 'info',
        summary: enabled ? 'Camera On' : 'Camera Off',
        life: 2000
      })
    }
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Camera Error',
      detail: 'Failed to access camera',
      life: 3000
    })
  }
}

async function toggleScreenShare() {
  try {
    if (currentUser.value?.mediaState.screen) {
      meetingStore.stopScreenShare()
      toast.add({
        severity: 'info',
        summary: 'Screen Share Stopped',
        life: 2000
      })
    } else {
      await meetingStore.startScreenShare()
      toast.add({
        severity: 'success',
        summary: 'Screen Share Started',
        life: 2000
      })
    }
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Screen Share Error',
      detail: 'Failed to start screen sharing',
      life: 3000
    })
  }
}

function toggleChat() {
  emit('toggleChat')
}

async function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (files && files.length > 0) {
    for (const file of Array.from(files)) {
      try {
        await meetingStore.sendFile(file)
        toast.add({
          severity: 'success',
          summary: 'File Sent',
          detail: `${file.name} is being sent`,
          life: 3000
        })
      } catch (error) {
        toast.add({
          severity: 'error',
          summary: 'File Send Error',
          detail: `Failed to send ${file.name}`,
          life: 3000
        })
      }
    }
    
    // Reset file input
    target.value = ''
  }
}

function leaveMeeting() {
  meetingStore.leaveMeeting()
  router.push('/')
}
</script>

<style scoped>
/* 使用 Tailwind CSS，移除自定义样式 */
</style>