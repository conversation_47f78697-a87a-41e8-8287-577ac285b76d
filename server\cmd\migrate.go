package cmd

import (
	"context"
	"github.com/urfave/cli/v2"
	"meeting/internal/entity"
	"meeting/pkg/config"
	"meeting/pkg/core"
)

func Migrate() *cli.Command {
	return &cli.Command{
		Name:  "migrate",
		Usage: "迁移",
		Flags: []cli.Flag{
			&cli.StringFlag{Name: "c", Value: "./config.toml", Usage: "配置文件"},
			&cli.BoolFlag{Name: "f", Value: false, Usage: "重建所有表结构"},
		},
		Action: func(c *cli.Context) error {
			config.InitializeConfig(c.String("c"))
			var entities = []any{
				&entity.APIKey{},
				//&entity.User{},
				//&entity.Meeting{},
			}
			core.InitializeDB()
			if c.<PERSON>ol("f") {
				if err := core.DB(context.Background()).Migrator().DropTable(entities...); err != nil {
					return err
				}
			}
			return core.DB(context.Background()).AutoMigrate(entities...)
		},
	}
}
