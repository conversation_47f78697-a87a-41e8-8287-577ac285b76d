.PHONY:all
all: build

.PHONY:build
build:
	go build -o ./bin/main .

.PHONY:build-linux
build-linux:
	GOOS=linux GOARCH=amd64 go build -o ./bin/linux-amd64 .

.PHONY:clean
clean:
	rm -rf ./bin/linux-amd64

.PHONY: swag
swag:
	swag init --parseDependency --parseInternal -o ./public ./api ./cmd

.PHONY: protocol
protocol:
	protoc --go_out=./pkg/protocol --go-grpc_out=./pkg/protocol ./pkg/protocol/proto/*.proto

.PHONY: vet
vet:
	go vet ./...

.PHONY: wire
wire:
	go run github.com/google/wire/cmd/wire