package middleware

import (
	"meeting/pkg/api"
	"meeting/pkg/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// APIKeyMiddleware API密钥验证中间件
func APIKeyMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 从请求头或查询参数中获取API密钥
		apiKey := ctx.GetHeader("X-API-Key")
		if apiKey == "" {
			apiKey = ctx.Query("api_key")
		}

		// 检查是否提供了API密钥
		if apiKey == "" {
			ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("Missing API key")))
			ctx.Abort()
			return
		}

		// 获取API密钥服务
		apiKeyService := services.GetServiceManager().GetAPIKeyService()

		// 验证API密钥是否存在且有效
		key, err := apiKeyService.GetAPIKeyByKey(apiKey)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("Invalid API key")))
			ctx.Abort()
			return
		}

		// 检查API密钥是否有足够的额度
		hasQuota, err := apiKeyService.HasQuota(apiKey)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage(err.Error())))
			ctx.Abort()
			return
		}

		if !hasQuota {
			ctx.JSON(http.StatusTooManyRequests, api.Fail(api.WithMessage("API key quota exceeded")))
			ctx.Abort()
			return
		}

		// 扣减额度
		if err = apiKeyService.UseQuota(key.ID); err != nil {
			ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("Failed to update quota")))
			ctx.Abort()
			return
		}

		// 将用户ID存储到上下文中供后续使用
		ctx.Set("user_id", key.UserID)
		ctx.Set("api_key_id", key.ID)
		ctx.Next()
	}
}
