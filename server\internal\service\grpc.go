package service

import (
	"context"
	"flag"
	"fmt"
	clientv3 "go.etcd.io/etcd/client/v3"
	"google.golang.org/grpc"
	"meeting/pkg/core"
	"meeting/pkg/protocol/pb"
	"net"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"
)

var (
	host     = "127.0.0.1"                                                        //服务器主机
	Port     = flag.Int("Port", 50051, "listening port")                          //服务器监听端口
	EtcdAddr = flag.String("EtcdAddr", "127.0.0.1:2379", "register etcd address") //etcd的地址
)
var cli *clientv3.Client

type chat struct {
	pb.UnimplementedChatServer
}

var Chat = new(chat)

func (s *chat) Connect(ctx context.Context, in *pb.ConnectRequest) (*pb.ConnectReply, error) {
	core.Logger().Infoln("Received: ", in.GetName())
	return &pb.ConnectReply{Message: "Hello " + in.Name}, nil
}

func (s *chat) SendMessage(ctx context.Context, in *pb.SendMessageRequest) (*pb.SendMessageReply, error) {
	core.Logger().Infoln("Send message" + in.GetContent())
	return &pb.SendMessageReply{Content: in.GetContent()}, nil
}

func RunGrpcServer() {
	serverAddr := fmt.Sprintf(":%d", 8990)
	lis, err := net.Listen("tcp", serverAddr)
	if err != nil {
		panic(err)
	}
	s := grpc.NewServer()
	defer s.GracefulStop()
	pb.RegisterChatServer(s, Chat)
	for _, m := range pb.Chat_ServiceDesc.Methods {
		_ = register(*EtcdAddr, pb.Chat_ServiceDesc.ServiceName, m.MethodName, serverAddr, 5)
	}

	//关闭信号处理
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, syscall.SIGTERM, syscall.SIGINT, syscall.SIGKILL, syscall.SIGHUP, syscall.SIGQUIT)
	go func() {
		s := <-ch
		for _, desc := range pb.Chat_ServiceDesc.Methods {
			unRegister(pb.Chat_ServiceDesc.ServiceName, desc.MethodName, serverAddr)
		}
		if i, ok := s.(syscall.Signal); ok {
			os.Exit(int(i))
		} else {
			os.Exit(0)
		}
	}()

	core.Logger().Fatalln(s.Serve(lis))
}

// 将服务地址注册到etcd中
func register(etcdAddr, serviceName, methodName, serverAddr string, ttl int64) error {
	var err error

	if cli == nil {
		//构建etcd client
		cli, err = clientv3.New(clientv3.Config{
			Endpoints:   strings.Split(etcdAddr, ";"),
			DialTimeout: 15 * time.Second,
		})
		if err != nil {
			fmt.Printf("连接etcd失败：%s\n", err)
			return err
		}
	}

	//与etcd建立长连接，并保证连接不断(心跳检测)
	ticker := time.NewTicker(time.Second * time.Duration(ttl))
	go func() {
		key := "/" + serviceName + "/" + methodName + "/" + serverAddr
		for {
			resp, err := cli.Get(context.Background(), key)
			//fmt.Printf("resp:%+v\n", resp)
			if err != nil {
				fmt.Printf("获取服务地址失败：%s", err)
			} else if resp.Count == 0 { //尚未注册
				err = keepAlive(serviceName, methodName, serverAddr, ttl)
				if err != nil {
					fmt.Printf("保持连接失败：%s", err)
				}
			}
			<-ticker.C
		}
	}()

	return nil
}

// 保持服务器与etcd的长连接
func keepAlive(serviceName, methodName, serverAddr string, ttl int64) error {
	//创建租约
	leaseResp, err := cli.Grant(context.Background(), ttl)
	if err != nil {
		fmt.Printf("创建租期失败：%s\n", err)
		return err
	}

	//将服务地址注册到etcd中
	key := "/" + serviceName + "/" + methodName + "/" + serverAddr
	_, err = cli.Put(context.Background(), key, serverAddr, clientv3.WithLease(leaseResp.ID))
	if err != nil {
		fmt.Printf("注册服务失败：%s", err)
		return err
	}

	//建立长连接
	ch, err := cli.KeepAlive(context.Background(), leaseResp.ID)
	if err != nil {
		fmt.Printf("建立长连接失败：%s\n", err)
		return err
	}

	//清空keepAlive返回的channel
	go func() {
		for {
			<-ch
		}
	}()
	return nil
}

// 取消注册
func unRegister(serviceName, methodName, serverAddr string) {
	if cli != nil {
		key := "/" + serviceName + "/" + methodName + "/" + serverAddr
		cli.Delete(context.Background(), key)
	}
}
