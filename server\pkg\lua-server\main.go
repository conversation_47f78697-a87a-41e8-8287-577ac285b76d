package main

import (
	"flag"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
)

// 配置参数
var (
	httpAddr   = flag.String("http", ":8080", "HTTP服务地址")
	nodeID     = flag.String("node", "node1", "节点ID")
	clusterURL = flag.String("cluster", "", "集群连接地址")
)

func main() {
	flag.Parse()

	// 初始化服务器
	server := NewServer(*nodeID)

	// 注册Lua API
	RegisterLuaAPI(server.LuaState)

	// 加载并执行Lua脚本
	if err := server.LoadScripts("scripts/"); err != nil {
		log.Fatalf("加载Lua脚本失败: %v", err)
	}

	// 启动分布式节点
	if *clusterURL != "" {
		if err := server.JoinCluster(*clusterURL); err != nil {
			log.Fatalf("加入集群失败: %v", err)
		}
	}

	// 注册HTTP处理函数
	http.HandleFunc("/ws", server.HandleWebSocket)

	// 启动HTTP服务器
	go func() {
		log.Printf("服务器启动，监听地址: %s", *httpAddr)
		if err := http.ListenAndServe(*httpAddr, nil); err != nil {
			log.Fatalf("HTTP服务器启动失败: %v", err)
		}
	}()

	// 优雅关闭
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	<-sigs

	log.Println("服务器正在关闭...")
	server.Shutdown()
	log.Println("服务器已关闭")
}
