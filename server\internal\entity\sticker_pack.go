package entity

import (
	"gorm.io/gorm"
	"time"
)

// StickerPack 表示贴纸包实体
type StickerPack struct {
	ID        uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	UUID      string          `gorm:"type:char(36);uniqueIndex" json:"uuid"`
	Name      string          `gorm:"type:varchar(255);not null" json:"name"`
	Image     string          `gorm:"type:varchar(255);not null" json:"image"`
	CreatedAt *time.Time      `gorm:"type:timestamp" json:"created_at,omitempty"`
	UpdatedAt *time.Time      `gorm:"type:timestamp" json:"updated_at,omitempty"`
	DeletedAt *gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at,omitempty"`
}
