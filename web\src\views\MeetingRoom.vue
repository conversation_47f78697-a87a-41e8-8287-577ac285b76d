<template>
  <div class="h-screen flex flex-col bg-gray-900 text-white relative">
    <div class="flex-1 flex flex-col">
      <VideoGrid />
      <ControlPanel @toggle-chat="toggleChat" />
    </div>
    
    <ChatPanel 
      :is-open="isChatOpen" 
      @close="closChat" 
    />
    
    <!-- Video Debug Panel -->
    <VideoDebugPanel />
    
    <!-- Loading overlay -->
    <div v-if="isJoining" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
      <div class="flex flex-col items-center gap-4 text-white">
        <ProgressSpinner />
        <p class="text-lg m-0">Joining meeting...</p>
      </div>
    </div>
  </div>
  <Toast />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ProgressSpinner from 'primevue/progressspinner'
import Toast from 'primevue/toast'
import { useToast } from 'primevue/usetoast'
import { useMeetingStore } from '@/stores/meeting'
import VideoGrid from '@/components/VideoGrid.vue'
import ControlPanel from '@/components/ControlPanel.vue'
import ChatPanel from '@/components/ChatPanel.vue'
import VideoDebugPanel from '@/components/VideoDebugPanel.vue'

const route = useRoute()
const router = useRouter()
const toast = useToast()
const meetingStore = useMeetingStore()

const isChatOpen = ref(false)
const isJoining = ref(false)

function toggleChat() {
  isChatOpen.value = !isChatOpen.value
}

function closChat() {
  isChatOpen.value = false
}

onMounted(async () => {
  const roomId = route.params.roomId as string
  const clientId = route.query.clientId as string || `user_${Date.now()}`
  
  if (!roomId) {
    toast.add({
      severity: 'error',
      summary: 'Invalid Room',
      detail: 'Room ID is required',
      life: 3000
    })
    router.push('/')
    return
  }

  try {
    isJoining.value = true
    
    // Get WebSocket URL from environment or use default
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/ws'
    
    await meetingStore.joinMeeting(roomId, clientId, wsUrl)
    
    toast.add({
      severity: 'success',
      summary: 'Joined Meeting',
      detail: `Connected to room ${roomId}`,
      life: 3000
    })
    
  } catch (error) {
    console.error('Failed to join meeting:', error)
    toast.add({
      severity: 'error',
      summary: 'Connection Failed',
      detail: 'Could not connect to the meeting',
      life: 5000
    })
    router.push('/')
  } finally {
    isJoining.value = false
  }
})

onUnmounted(() => {
  meetingStore.leaveMeeting()
})

// Handle page refresh/close
window.addEventListener('beforeunload', () => {
  meetingStore.leaveMeeting()
})
</script>

<style scoped>
/* 使用 Tailwind CSS，移除自定义样式 */
</style>