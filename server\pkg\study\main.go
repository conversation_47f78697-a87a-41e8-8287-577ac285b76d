package main

import (
	"fmt"
	"reflect"
)

type User struct {
	Name   string
	Gender string
}

func main() {
	type Person struct {
		Name  string
		Age   int
		Email string
	}

	m := map[string]interface{}{
		"Name":  "<PERSON>",
		"Age":   28,
		"Email": "<EMAIL>",
	}

	p := Person{}
	v := reflect.ValueOf(&p).Elem()
	for k, mv := range m {
		f := v.FieldByName(k)
		if f.<PERSON>() {
			if f.CanSet() {
				switch f.Kind() {
				case reflect.String:
					f.SetString(mv.(string))
				case reflect.Int:
					f.SetInt(int64(mv.(int)))
				default:
					fmt.Printf("unsupported type %v\n", f.Kind())
				}
			} else {
				fmt.Printf("field %v is not settable\n", k)
			}
		} else {
			fmt.Printf("field %v does not exist\n", k)
		}
	}

	fmt.Println(p)
}
