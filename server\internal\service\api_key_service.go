package service

import (
	"context"
	"fmt"
	"meeting/internal/entity"
	"meeting/pkg/core"
	"meeting/pkg/utils"
	"time"

	"gorm.io/gorm"
)

type APIKeyService struct {
	db *gorm.DB
}

func NewAPIKeyService() *APIKeyService {
	return &APIKeyService{
		db: core.DB(context.Background()),
	}
}

// GenerateAPIKey 为用户生成一个新的API密钥
func (s *APIKeyService) GenerateAPIKey(userID uint, name string, quota uint) (*entity.APIKey, error) {
	// 生成唯一的API密钥
	apiKey := &entity.APIKey{
		UserID:    userID,
		Name:      name,
		Key:       utils.GenerateRandomString(32),
		Quota:     quota,
		UsedQuota: 0,
		IsActive:  true,
	}

	// 保存到数据库
	if err := s.db.Create(apiKey).Error; err != nil {
		return nil, fmt.Errorf("failed to create API key: %w", err)
	}

	return apiKey, nil
}

// GetAPIKeyByKey 根据API密钥字符串获取API密钥信息
func (s *APIKeyService) GetAPIKeyByKey(key string) (*entity.APIKey, error) {
	var apiKey entity.APIKey
	if err := s.db.Where("`key` = ? AND `is_active` = ?", key, true).First(&apiKey).Error; err != nil {
		return nil, err
	}
	return &apiKey, nil
}

// UseQuota 使用API密钥的额度
func (s *APIKeyService) UseQuota(keyID uint) error {
	return s.db.Model(&entity.APIKey{}).Where("id = ?", keyID).Update("used_quota", gorm.Expr("used_quota + ?", 1)).Error
}

// HasQuota 检查API密钥是否有足够的额度
func (s *APIKeyService) HasQuota(key string) (bool, error) {
	apiKey, err := s.GetAPIKeyByKey(key)
	if err != nil {
		return false, err
	}

	// 检查是否过期
	if apiKey.ExpiresAt != nil && apiKey.ExpiresAt.Before(time.Now()) {
		return false, fmt.Errorf("api key has expired")
	}

	// 检查额度是否足够
	return apiKey.UsedQuota < apiKey.Quota, nil
}

// GetAPIKeysByUserID 获取用户的所有API密钥
func (s *APIKeyService) GetAPIKeysByUserID(userID string) ([]entity.APIKey, error) {
	var apiKeys []entity.APIKey
	if err := s.db.Where("user_id = ?", userID).Find(&apiKeys).Error; err != nil {
		return nil, err
	}
	return apiKeys, nil
}

// DeleteAPIKey 删除API密钥
func (s *APIKeyService) DeleteAPIKey(id, userID string) error {
	return s.db.Where("id = ? AND user_id = ?", id, userID).Delete(&entity.APIKey{}).Error
}
