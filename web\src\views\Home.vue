<template>
  <div class="min-h-screen bg-gradient-to-br from-indigo-500 via-purple-500 to-purple-700 flex items-center justify-center p-5 relative">
    <!-- Meeting Entry Modal -->
    <Dialog
      v-if="!meetingStore.inMeeting"
      v-model:visible="showMeetingEntry"
      modal
      :header="t('tools.webRtcMeeting.title')"
      :style="{ width: '500px' }"
      :draggable="false"
      :closeOnEscape="false"
      :closable="false"
    >
      <MeetingEntry
        :connection-state="meetingStore.connectionState"
        @create-meeting="handleCreateMeeting"
        @join-meeting="handleJoinMeeting"
        ref="meetingEntryRef"
      />
    </Dialog>

    <!-- Background content when not in meeting -->
    <div v-if="!meetingStore.inMeeting" class="w-full max-w-6xl">
      <div class="text-center text-white">
        <h1 class="text-5xl md:text-6xl font-bold mb-4 text-shadow">
          WebRTC Meeting
        </h1>
        <p class="text-xl md:text-2xl mb-12 opacity-90 text-shadow-sm">
          Start or join a video meeting with screen sharing, file transfer, and chat
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16">
          <div class="feature-card">
            <i class="pi pi-video text-5xl mb-4 text-yellow-400"></i>
            <h3 class="text-xl font-semibold mb-2">Video & Audio</h3>
            <p class="opacity-90 leading-relaxed">High-quality video calls with crystal clear audio</p>
          </div>
          
          <div class="feature-card">
            <i class="pi pi-desktop text-5xl mb-4 text-yellow-400"></i>
            <h3 class="text-xl font-semibold mb-2">Screen Sharing</h3>
            <p class="opacity-90 leading-relaxed">Share your screen for presentations and collaboration</p>
          </div>
          
          <div class="feature-card">
            <i class="pi pi-comments text-5xl mb-4 text-yellow-400"></i>
            <h3 class="text-xl font-semibold mb-2">Real-time Chat</h3>
            <p class="opacity-90 leading-relaxed">Send text messages during the meeting</p>
          </div>
          
          <div class="feature-card">
            <i class="pi pi-file text-5xl mb-4 text-yellow-400"></i>
            <h3 class="text-xl font-semibold mb-2">File Transfer</h3>
            <p class="opacity-90 leading-relaxed">Share files directly through WebRTC data channels</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Toast />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import { useMeetingStore } from '@/stores/meeting'
import MeetingEntry from '@/components/meeting/MeetingEntry.vue'

const router = useRouter()
const { t } = useI18n()
const meetingStore = useMeetingStore()

// Reactive variables
const showMeetingEntry = ref(true)

// Component refs
const meetingEntryRef = ref()

// Initialize services
const initializeServices = async () => {
  try {
    // Generate device ID
    meetingStore.localDeviceId = Math.random().toString(36).substring(2, 11)

    // Set connection state to connecting
    meetingStore.setConnectionState('connecting')

    // Simulate connection (replace with actual WebRTC service initialization)
    setTimeout(() => {
      meetingStore.setConnectionState('connected')
    }, 1000)
  } catch (error) {
    console.error('Failed to initialize services:', error)
    meetingStore.setConnectionState('disconnected')
    meetingEntryRef.value?.setError(t('tools.webRtcMeeting.errors.connectionFailed'))
  }
}

// Event handlers for components
const handleCreateMeeting = (displayName: string) => {
  meetingStore.displayName = displayName

  // Generate a new meeting ID
  const meetingId = Math.random().toString(36).substring(2, 10).toUpperCase()

  // Navigate to meeting room
  router.push({
    path: `/meeting/${meetingId}`,
    query: { clientId: displayName }
  })
}

const handleJoinMeeting = (meetingId: string, displayName: string) => {
  meetingStore.displayName = displayName

  // Navigate to meeting room
  router.push({
    path: `/meeting/${meetingId}`,
    query: { clientId: displayName }
  })
}

// Initialize on mount
onMounted(async () => {
  await initializeServices()
})

// Cleanup on unmount
onUnmounted(() => {
  // Add cleanup logic here if needed
})
</script>

<style scoped>
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.feature-card {
  @apply bg-white bg-opacity-10 backdrop-blur-lg rounded-xl p-8 text-center border border-white border-opacity-20;
}
</style>
