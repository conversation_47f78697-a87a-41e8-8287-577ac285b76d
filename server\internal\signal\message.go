package signal

type MessageType string

const (
	MessageTypeJoin        MessageType = "join"
	MessageTypeLeave       MessageType = "leave"
	MessageTypeAllClients  MessageType = "all-clients"
	MessageTypeWebRTCEvent MessageType = "webrtc-event"
	MessageTypeChat        MessageType = "chat"
)

// Message represents a message to be sent to clients
type Message struct {
	Type     MessageType `json:"type"`
	From     *Client     `json:"from"`
	To       *Client     `json:"to"` // todo 这里需要修改，不一定只是发送给一个人
	Data     any         `json:"data,omitempty"`
	receiver Receiver
}

type Receiver interface {
	Can(c *Client) bool
}

type RoleReceiver struct {
	Role Role
}

func (r *RoleReceiver) Can(c *Client) bool {
	return c.HasRole(r.Role)
}
