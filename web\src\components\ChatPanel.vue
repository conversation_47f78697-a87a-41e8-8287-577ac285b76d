<template>
  <div class="fixed top-0 w-96 h-screen bg-white border-l border-gray-200 flex flex-col transition-all duration-300 z-40" 
       :class="isOpen ? 'right-0' : '-right-96'">
    <div class="flex justify-between items-center px-5 py-4 border-b border-gray-200 bg-gray-50">
      <h3 class="m-0 text-lg font-semibold">Chat</h3>
      <Button
        icon="pi pi-times"
        @click="$emit('close')"
        text
        rounded
        size="small"
      />
    </div>

    <div class="flex-1 overflow-y-auto p-4 flex flex-col gap-3" ref="messagesContainer">
      <div
        v-for="message in chatMessages"
        :key="message.id"
        class="flex flex-col gap-1"
        :class="{ 'items-end': message.senderId === clientId }"
      >
        <div class="flex gap-2 items-center text-xs text-gray-500"
             :class="{ 'flex-row-reverse': message.senderId === clientId }">
          <span class="font-medium">{{ message.senderName }}</span>
          <span>{{ formatTime(message.timestamp) }}</span>
        </div>
        <div class="max-w-[80%] px-3 py-2 rounded-xl break-words flex items-center gap-2"
             :class="message.senderId === clientId 
               ? 'bg-blue-500 text-white' 
               : message.type === 'file' 
                 ? 'bg-yellow-100 text-yellow-800' 
                 : 'bg-gray-100'">
          <i v-if="message.type === 'file'" class="pi pi-file text-sm" />
          {{ message.content }}
        </div>
      </div>
      
      <div v-if="chatMessages.length === 0" class="flex flex-col items-center justify-center h-full text-gray-400 text-center">
        <i class="pi pi-comments text-5xl mb-4" />
        <p>No messages yet. Start the conversation!</p>
      </div>
    </div>

    <div v-if="activeTransfers.length > 0" class="px-4 py-4 border-t border-gray-200 bg-gray-50">
      <h4 class="m-0 mb-3 text-sm font-semibold text-gray-700">File Transfers</h4>
      <div
        v-for="transfer in activeTransfers"
        :key="transfer.id"
        class="flex flex-col gap-2 p-3 bg-white rounded-lg border border-gray-200"
      >
        <div class="flex items-center gap-2 text-sm">
          <i class="pi pi-file" />
          <span class="font-medium flex-1">{{ transfer.name }}</span>
          <span class="text-gray-500 text-xs">({{ formatFileSize(transfer.size) }})</span>
        </div>
        <div class="flex items-center gap-2">
          <ProgressBar :value="transfer.progress" class="flex-1 h-1.5" />
          <span class="text-xs text-gray-500 min-w-[40px] text-right">{{ Math.round(transfer.progress) }}%</span>
        </div>
      </div>
    </div>

    <div class="flex gap-2 p-4 border-t border-gray-200 bg-gray-50">
      <InputText
        v-model="newMessage"
        placeholder="Type a message..."
        @keyup.enter="sendMessage"
        class="flex-1"
      />
      <Button
        icon="pi pi-send"
        @click="sendMessage"
        :disabled="!newMessage.trim()"
        rounded
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import ProgressBar from 'primevue/progressbar'
import { useMeetingStore } from '@/stores/meeting'

const props = defineProps<{
  isOpen: boolean
}>()

defineEmits<{
  close: []
}>()

const meetingStore = useMeetingStore()

const newMessage = ref('')
const messagesContainer = ref<HTMLElement>()

const chatMessages = computed(() => meetingStore.chatMessages)
const activeTransfers = computed(() => meetingStore.activeTransfers)
const clientId = computed(() => meetingStore.clientId)

function sendMessage() {
  const content = newMessage.value.trim()
  if (content) {
    meetingStore.sendChatMessage(content)
    newMessage.value = ''
  }
}

function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Auto-scroll to bottom when new messages arrive
watch(chatMessages, () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}, { deep: true })

// Mark messages as read when chat is opened
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    meetingStore.markMessagesAsRead()
  }
})
</script>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .fixed {
    width: 100vw;
  }
  
  .fixed:not(.right-0) {
    right: -100vw;
  }
}
</style>