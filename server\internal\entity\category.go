package entity

import (
	"gorm.io/gorm"
	"time"
)

// Category 表示分类实体，映射到 categories 表
type Category struct {
	ID          uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	UUID        string          `gorm:"type:char(36);uniqueIndex" json:"uuid"`
	Name        string          `gorm:"type:varchar(255);not null;default:''" json:"name"`
	Slug        *string         `gorm:"type:varchar(255);comment:别名" json:"slug,omitempty"`
	Description *string         `gorm:"type:varchar(255)" json:"description,omitempty"`
	Icon        *string         `gorm:"type:varchar(255)" json:"icon,omitempty"`
	Order       uint            `gorm:"type:mediumint(8) unsigned;not null;default:0" json:"order"`
	ParentID    string          `gorm:"type:char(36);not null;default:'0'" json:"parent_id"`
	Enabled     uint8           `gorm:"type:tinyint(1) unsigned;not null;default:1" json:"enabled"`
	CreatedAt   *time.Time      `gorm:"type:timestamp" json:"created_at,omitempty"`
	UpdatedAt   *time.Time      `gorm:"type:timestamp" json:"updated_at,omitempty"`
	DeletedAt   *gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at,omitempty"`
	
	// 关联贴纸（多对多关系）
	Stickers []Sticker `gorm:"many2many:sticker_categories;" json:"stickers,omitempty"`
}