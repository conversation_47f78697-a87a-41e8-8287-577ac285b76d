package handler

import (
	"context"
	"fmt"
	"meeting/pkg/protocol/pb"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type MCPHandler struct{}

var MCP = &MCPHandler{}

func (*MCPHandler) CallMCP(ctx *gin.Context) {
	conn, err := grpc.NewClient(":8990", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err == nil {
		defer conn.Close()
		cc := pb.NewChatClient(conn)
		res, err := cc.Connect(context.Background(), &pb.ConnectRequest{
			Name: "Hell",
			Role: 1,
		})
		if err != nil {
			fmt.Printf("调用服务端代码失败: %s", err)
			return
		}
		fmt.Println(res)
	}
}
