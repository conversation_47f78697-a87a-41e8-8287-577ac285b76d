package signature

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"net/url"
	"strconv"
	"time"
)

type Parameters struct {
	AppID   string    `json:"AppID" binding:"required"`
	UserID  string    `json:"UserID" binding:"required"`
	Role    byte      `json:"Role" binding:"required"` // todo Role = 0 的时候required会报错
	Expires time.Time `json:"Expires" binding:"required"`
}

var (
	ErrSignatureExpired = errors.New("signature expired")
	ErrInvalidSignature = errors.New("invalid signature")
)

type VerificationParameters struct {
	*Parameters
	Signature string `json:"Signature"`
}

func Sign(p *Parameters, secretKey string) (string, error) {
	query := "AppID=" + p.AppID +
		"&UserID=" + p.UserID +
		"&Role=" + strconv.Itoa(int(p.Role)) +
		"&Expires=" + url.QueryEscape(p.Expires.Format(time.RFC3339))
	bs := base64.StdEncoding.EncodeToString([]byte(query))
	hs := hmac.New(sha256.New, []byte(secretKey))
	hs.Write([]byte(bs))

	return hex.EncodeToString(hs.Sum(nil)), nil
}

func Verify(p *VerificationParameters) error {
	if p.Expires.Before(time.Now()) {
		return ErrSignatureExpired
	}

	sig, err := Sign(&Parameters{
		AppID:   p.AppID,
		UserID:  p.UserID,
		Role:    p.Role,
		Expires: p.Expires,
	}, "secret")
	if err != nil {
		return err
	}

	if !hmac.Equal([]byte(sig), []byte(p.Signature)) {
		return ErrInvalidSignature
	}

	return nil
}
