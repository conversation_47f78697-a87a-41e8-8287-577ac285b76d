package name

import (
	"context"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

func tool() mcp.Tool {
	return mcp.NewTool("name",
		mcp.WithDescription("Get my name; 获取我的名字"),
	)
}

func handler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return mcp.NewToolResultText("张三"), nil
}

func Tool() (mcp.Tool, server.ToolHandlerFunc) {
	return tool(), handler
}
