package cmd

import (
	"fmt"
	"meeting/api/router"
	"meeting/pkg/config"
	"meeting/pkg/core"
	"net/http"
)

// Application 应用程序结构体
type Application struct {
	Server *http.Server
	Config *config.TomlConfig
}

// InitializeApplication 初始化应用程序（简化版本）
func InitializeApplication(configPath string) (*Application, error) {
	// 初始化配置
	cfg, err := config.NewConfig(configPath)
	if err != nil {
		return nil, err
	}

	// 设置全局配置（为了向后兼容）
	config.SetGlobalConfig(cfg)

	// 初始化数据库和日志（为了向后兼容）
	config.InitializeConfig(configPath)
	core.InitializeDB()
	core.InitializeLogger()

	// 创建HTTP服务器
	server := NewHTTPServer(cfg)

	return &Application{
		Server: server,
		Config: cfg,
	}, nil
}

// NewHTTPServer 创建HTTP服务器
func NewHTTPServer(cfg *config.TomlConfig) *http.Server {
	// 设置全局配置（为了向后兼容）
	config.SetGlobalConfig(cfg)

	host := cfg.App.Host
	if host == "" {
		host = "0.0.0.0"
	}
	port := cfg.App.Port
	if port == 0 {
		port = 8989
	}

	return &http.Server{
		Addr:    fmt.Sprintf("%s:%d", host, port),
		Handler: router.NewRouter(),
	}
}
