package repository

import (
	"context"
	"meeting/internal/entity"

	"gorm.io/gorm"
)

// StickerRepository 贴纸仓库接口
type StickerRepository interface {
	GetAll(ctx context.Context) ([]*entity.Sticker, error)
	GetByID(ctx context.Context, id uint) (*entity.Sticker, error)
	Create(ctx context.Context, sticker *entity.Sticker) error
	Update(ctx context.Context, sticker *entity.Sticker) error
	Delete(ctx context.Context, id uint) error
	FindByUUID(ctx context.Context, uuid string) (*entity.Sticker, error)
	IncrementViews(ctx context.Context, id uint) error
	FindFavoriteByUserAndModel(ctx context.Context, userID uint, modelType string, modelID uint) (*entity.Favorite, error)
	FindStickers(ctx context.Context, offset, limit int, keyword, categoryID string) ([]entity.Sticker, error)
	CountStickers(ctx context.Context, keyword, categoryID string) (int64, error)
	CreateFavorite(ctx context.Context, favorite *entity.Favorite) error
	DeleteFavorite(ctx context.Context, userID uint, modelType string, modelID uint) error
}

// stickerRepository 贴纸仓库实现
type stickerRepository struct {
	db *gorm.DB
}

// NewStickerRepository 创建贴纸仓库
func NewStickerRepository(db *gorm.DB) StickerRepository {
	return &stickerRepository{db: db}
}

func (r *stickerRepository) GetAll(ctx context.Context) ([]*entity.Sticker, error) {
	var stickers []*entity.Sticker
	err := r.db.WithContext(ctx).Find(&stickers).Error
	return stickers, err
}

func (r *stickerRepository) GetByID(ctx context.Context, id uint) (*entity.Sticker, error) {
	var sticker entity.Sticker
	err := r.db.WithContext(ctx).First(&sticker, id).Error
	return &sticker, err
}

func (r *stickerRepository) Create(ctx context.Context, sticker *entity.Sticker) error {
	return r.db.WithContext(ctx).Create(sticker).Error
}

func (r *stickerRepository) Update(ctx context.Context, sticker *entity.Sticker) error {
	return r.db.WithContext(ctx).Save(sticker).Error
}

func (r *stickerRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&entity.Sticker{}, id).Error
}

// FindByUUID 根据UUID查找贴纸并预加载贴纸包
func (r *stickerRepository) FindByUUID(ctx context.Context, uuid string) (*entity.Sticker, error) {
	var sticker entity.Sticker
	err := r.db.WithContext(ctx).Model(&entity.Sticker{}).
		Where("uuid = ?", uuid).
		First(&sticker).Error

	if err != nil {
		return nil, err
	}

	return &sticker, nil
}

// IncrementViews 增加贴纸浏览次数
func (r *stickerRepository) IncrementViews(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&entity.Sticker{}).
		Where("id = ?", id).
		Update("views", gorm.Expr("views + ?", 1)).Error
}

// FindFavoriteByUserAndModel 根据用户ID和模型信息查找收藏记录
func (r *stickerRepository) FindFavoriteByUserAndModel(ctx context.Context, userID uint, modelType string, modelID uint) (*entity.Favorite, error) {
	var favorite entity.Favorite
	err := r.db.WithContext(ctx).Where("user_id = ? AND model_type = ? AND model_id = ?",
		userID, modelType, modelID).First(&favorite).Error

	if err != nil {
		return nil, err
	}

	return &favorite, nil
}

// FindStickers 查找贴纸列表
func (r *stickerRepository) FindStickers(ctx context.Context, offset, limit int, keyword, categoryID string) ([]entity.Sticker, error) {
	var stickers []entity.Sticker

	db := r.db.WithContext(ctx).Model(&entity.Sticker{})

	// 根据关键字筛选
	if keyword != "" {
		db = db.Where("name LIKE ?", "%"+keyword+"%")
	}

	// 根据分类ID筛选
	if categoryID != "" {
		db = db.Where("exists(select * from categories c join model_has_categories mc on c.id = mc.category_id and mc.model_type = 'sticker' where mc.model_id = `stickers`.id and c.uuid = ?)", categoryID)
	}

	// 执行查询
	err := db.Order("RAND()").Offset(offset).Limit(limit).Find(&stickers).Error
	return stickers, err
}

// CountStickers 计算贴纸总数
func (r *stickerRepository) CountStickers(ctx context.Context, keyword, categoryID string) (int64, error) {
	var total int64

	db := r.db.WithContext(ctx).Model(&entity.Sticker{})

	// 根据关键字筛选
	if keyword != "" {
		db = db.Where("name LIKE ?", "%"+keyword+"%")
	}

	// 根据分类ID筛选
	if categoryID != "" {
		db = db.Where("exists(select * from categories c join model_has_categories mc on c.id = mc.category_id and mc.model_type = 'sticker' where mc.model_id = `stickers`.id)")
	}

	err := db.Count(&total).Error
	return total, err
}

// CreateFavorite 创建收藏记录
func (r *stickerRepository) CreateFavorite(ctx context.Context, favorite *entity.Favorite) error {
	return r.db.WithContext(ctx).Create(favorite).Error
}

// DeleteFavorite 删除收藏记录
func (r *stickerRepository) DeleteFavorite(ctx context.Context, userID uint, modelType string, modelID uint) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND model_type = ? AND model_id = ?",
		userID, modelType, modelID).Delete(&entity.Favorite{}).Error
}
