<template>
  <div class="p-4">
    <div v-if="connectionState !== 'connected'" class="flex items-center gap-3 p-4 bg-surface-100 rounded-lg mb-6">
      <div class="flex items-center">
        <ProgressSpinner v-if="connectionState === 'connecting'" size="small" />
        <i v-else-if="connectionState === 'disconnected'" class="pi pi-exclamation-triangle text-red-500"></i>
        <i v-else-if="connectionState === 'connected'" class="pi pi-check-circle text-green-500"></i>
      </div>
      <span class="font-medium text-surface-700">
        {{ getConnectionStatusText() }}
      </span>
    </div>

    <div v-if="connectionState === 'connected'" class="flex flex-col gap-6">
      <div class="flex flex-col gap-2">
        <label for="displayName" class="font-semibold text-surface-700">
          {{ t('tools.webRtcMeeting.entry.displayName') }}
        </label>
        <InputText
          id="displayName"
          v-model="displayName"
          :placeholder="t('tools.webRtcMeeting.entry.displayNamePlaceholder')"
          class="w-full"
          @keyup.enter="handleQuickJoin"
        />
      </div>

      <div class="flex flex-col gap-2">
        <label for="meetingId" class="font-semibold text-surface-700">
          {{ t('tools.webRtcMeeting.entry.meetingId') }}
        </label>
        <div class="flex gap-2">
          <InputText
            id="meetingId"
            v-model="meetingId"
            :placeholder="t('tools.webRtcMeeting.entry.meetingIdPlaceholder')"
            class="flex-1"
            @keyup.enter="handleJoinMeeting"
          />
        </div>
      </div>

      <div class="flex flex-col gap-3">
        <Button
          :label="t('tools.webRtcMeeting.entry.createMeeting')"
          icon="pi pi-plus"
          @click="handleCreateMeeting"
          :disabled="!displayName.trim()"
          severity="success"
          class="w-full"
        />
        
        <Button
          :label="t('tools.webRtcMeeting.entry.joinMeeting')"
          icon="pi pi-sign-in"
          @click="handleJoinMeeting"
          :disabled="!displayName.trim() || !meetingId.trim()"
          class="w-full"
        />
      </div>

      <div v-if="errorMessage" class="flex items-center gap-2 p-3 bg-red-50 text-red-700 rounded-md border-l-4 border-red-500">
        <i class="pi pi-exclamation-triangle text-red-500"></i>
        <span>{{ errorMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import ProgressSpinner from 'primevue/progressspinner'

interface Props {
  connectionState: 'connecting' | 'connected' | 'disconnected'
}

interface Emits {
  (e: 'create-meeting', displayName: string): void
  (e: 'join-meeting', meetingId: string, displayName: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

const displayName = ref('')
const meetingId = ref('')
const errorMessage = ref('')

const getConnectionStatusText = () => {
  switch (props.connectionState) {
    case 'connecting':
      return t('tools.webRtcMeeting.connection.connecting')
    case 'connected':
      return t('tools.webRtcMeeting.connection.connected')
    case 'disconnected':
      return t('tools.webRtcMeeting.connection.disconnected')
    default:
      return ''
  }
}

const handleCreateMeeting = () => {
  if (!displayName.value.trim()) {
    setError(t('tools.webRtcMeeting.errors.displayNameRequired'))
    return
  }
  
  clearError()
  emit('create-meeting', displayName.value.trim())
}

const handleJoinMeeting = () => {
  if (!displayName.value.trim()) {
    setError(t('tools.webRtcMeeting.errors.displayNameRequired'))
    return
  }
  
  if (!meetingId.value.trim()) {
    setError(t('tools.webRtcMeeting.errors.meetingIdRequired'))
    return
  }
  
  clearError()
  emit('join-meeting', meetingId.value.trim(), displayName.value.trim())
}

const handleQuickJoin = () => {
  if (meetingId.value.trim()) {
    handleJoinMeeting()
  } else {
    handleCreateMeeting()
  }
}

const setError = (message: string) => {
  errorMessage.value = message
}

const clearError = () => {
  errorMessage.value = ''
}

// Expose methods for parent component
defineExpose({
  setError,
  clearError
})
</script>

<style scoped>
/* 使用 Tailwind CSS，只保留必要的自定义样式 */
</style>