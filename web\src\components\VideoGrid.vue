<template>
  <div class="video-grid" :class="gridClass">
    <!-- Local video -->
    <div class="video-tile local-video">
      <video
        ref="localVideoRef"
        autoplay
        muted
        playsinline
        class="video-element local"
        @loadedmetadata="onLocalVideoLoaded"
        @error="onLocalVideoError"
      />
      <div class="video-overlay">
        <div class="participant-info">
          <span class="participant-name">You</span>
          <div class="media-indicators">
            <i v-if="!currentUser?.mediaState.audio" class="pi pi-microphone-slash muted" />
            <i v-if="!currentUser?.mediaState.video" class="pi pi-video-slash video-off" />
            <i v-if="currentUser?.mediaState.screen" class="pi pi-desktop screen-share" />
          </div>
        </div>
      </div>
      <div v-if="localVideoState.isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <span>Starting video...</span>
      </div>
    </div>

    <!-- Remote videos -->
    <div
      v-for="participant in remoteParticipants"
      :key="participant.id"
      class="video-tile remote-video"
    >
      <video
        :ref="el => setRemoteVideoRef(participant.id, el)"
        autoplay
        playsinline
        class="video-element remote"
        @loadedmetadata="() => onRemoteVideoLoaded(participant.id)"
        @error="(e) => onRemoteVideoError(participant.id, e)"
        @loadstart="() => onRemoteVideoLoadStart(participant.id)"
      />
      
      <!-- Loading state -->
      <div v-if="getVideoState(participant.id).isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <span>Connecting...</span>
      </div>
      
      <!-- Error state -->
      <div v-else-if="getVideoState(participant.id).hasError" class="error-overlay">
        <i class="pi pi-exclamation-triangle"></i>
        <span>Connection failed</span>
        <button @click="retryConnection(participant.id)" class="retry-button">
          Retry
        </button>
      </div>
      
      <!-- No video placeholder -->
      <div v-else-if="!participant.mediaState.video" class="no-video-overlay">
        <div class="avatar-placeholder">
          {{ getParticipantInitials(participant.name) }}
        </div>
        <span class="participant-name">{{ participant.name }}</span>
      </div>

      <div class="video-overlay">
        <div class="participant-info">
          <span class="participant-name">{{ participant.name }}</span>
          <div class="media-indicators">
            <i v-if="!participant.mediaState.audio" class="pi pi-microphone-slash muted" />
            <i v-if="!participant.mediaState.video" class="pi pi-video-slash video-off" />
            <i v-if="participant.mediaState.screen" class="pi pi-desktop screen-share" />
            <div class="connection-indicator" :class="getConnectionQuality(participant.id)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick, onUnmounted, reactive } from 'vue'
import { useMeetingStore } from '@/stores/meeting'
import { streamManager, type StreamAssignmentResult } from '@/utils/streamManager'
import { videoLogger, startVideoTiming, endVideoTiming } from '@/utils/videoLogger'

// Types for video state management
interface VideoState {
  isLoading: boolean
  hasError: boolean
  errorMessage?: string
  retryCount: number
  lastRetryTime: number
  isPlaying: boolean
}

const meetingStore = useMeetingStore()

// Refs for video elements
const localVideoRef = ref<HTMLVideoElement>()
const remoteVideoRefs = ref<Map<string, HTMLVideoElement>>(new Map())

// State management for video tiles
const localVideoState = reactive<VideoState>({
  isLoading: false,
  hasError: false,
  retryCount: 0,
  lastRetryTime: 0,
  isPlaying: false
})

const remoteVideoStates = ref<Map<string, VideoState>>(new Map())

// Computed properties
const localStream = computed(() => meetingStore.localStream || meetingStore.screenStream)
const currentUser = computed(() => meetingStore.currentUser)
const remoteParticipants = computed(() => 
  meetingStore.participantsList.filter(p => p.id !== meetingStore.clientId)
)

const gridClass = computed(() => {
  const totalParticipants = meetingStore.participantsList.length
  const baseClass = 'grid gap-4 h-full w-full p-6'
  
  if (totalParticipants <= 1) return `${baseClass} grid-cols-1`
  if (totalParticipants <= 2) return `${baseClass} grid-cols-2`
  if (totalParticipants <= 4) return `${baseClass} grid-cols-2 grid-rows-2`
  if (totalParticipants <= 6) return `${baseClass} grid-cols-3 grid-rows-2`
  return `${baseClass} grid-cols-[repeat(auto-fit,minmax(300px,1fr))]`
})

// Video element management with comprehensive logging
function setRemoteVideoRef(participantId: string, el: any) {
  if (el) {
    const videoElement = el as HTMLVideoElement
    remoteVideoRefs.value.set(participantId, videoElement)
    
    videoLogger.debug('VIDEO_REF', `Video element created for ${participantId}`, {
      elementId: videoElement.id,
      className: videoElement.className
    }, participantId)
    
    // Log initial video element state
    videoLogger.logVideoElementState(participantId, videoElement, 'Element created')
    
    // Initialize video state if not exists
    if (!remoteVideoStates.value.has(participantId)) {
      remoteVideoStates.value.set(participantId, {
        isLoading: true,
        hasError: false,
        retryCount: 0,
        lastRetryTime: 0,
        isPlaying: false
      })
      
      videoLogger.info('VIDEO_STATE', `Initialized video state for ${participantId}`, getVideoState(participantId), participantId)
    }
    
    // Immediately try to assign stream if available
    const stream = meetingStore.remoteStreams.get(participantId)
    if (stream) {
      videoLogger.info('STREAM_ASSIGNMENT', `Stream available for immediate assignment to ${participantId}`, undefined, participantId)
      videoLogger.logStreamInfo(participantId, stream, 'Immediate assignment')
      assignStreamToElement(videoElement, stream, participantId)
    } else {
      videoLogger.debug('STREAM_ASSIGNMENT', `No stream available yet for ${participantId}`, undefined, participantId)
    }
  } else {
    // Cleanup when element is removed
    if (remoteVideoRefs.value.has(participantId)) {
      videoLogger.info('VIDEO_REF', `Video element removed for ${participantId}`, undefined, participantId)
    }
    
    remoteVideoRefs.value.delete(participantId)
    remoteVideoStates.value.delete(participantId)
  }
}

// Enhanced stream assignment with comprehensive logging
async function assignStreamToElement(
  element: HTMLVideoElement, 
  stream: MediaStream, 
  participantId?: string
): Promise<void> {
  const timingKey = `stream-assignment-${participantId || 'local'}`
  startVideoTiming(timingKey)
  
  if (!participantId) {
    // Handle local stream assignment (simpler case)
    try {
      localVideoState.isLoading = true
      localVideoState.hasError = false
      
      videoLogger.info('LOCAL_STREAM', 'Starting local stream assignment', {
        streamId: stream.id,
        tracks: stream.getTracks().length
      })
      
      videoLogger.logStreamInfo('local', stream, 'Local assignment start')
      videoLogger.logVideoElementState('local', element, 'Before local assignment')
      
      const result = await streamManager.assignStreamToElement(
        element, 
        stream, 
        'local',
        { maxRetries: 2, timeout: 3000 }
      )
      
      if (result.success) {
        localVideoState.isLoading = false
        localVideoState.isPlaying = true
        
        const duration = endVideoTiming(timingKey, 'LOCAL_STREAM', 'Local stream assignment completed')
        videoLogger.updateMetrics('local', {
          streamAssignmentTime: duration,
          successRate: 100
        })
        
        videoLogger.logVideoElementState('local', element, 'After successful local assignment')
      } else {
        throw result.error || new Error('Stream assignment failed')
      }
    } catch (error) {
      localVideoState.isLoading = false
      localVideoState.hasError = true
      localVideoState.errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      videoLogger.error('LOCAL_STREAM', 'Local stream assignment failed', error)
      videoLogger.updateMetrics('local', { errorCount: 1, successRate: 0 })
      
      throw error
    }
    return
  }

  // Handle remote stream assignment
  const state = getVideoState(participantId)
  
  try {
    videoLogger.info('REMOTE_STREAM', `Starting remote stream assignment for ${participantId}`, {
      currentState: { ...state },
      streamActive: stream.active,
      trackCount: stream.getTracks().length
    }, participantId)
    
    // Validate stream before assignment
    if (!streamManager.validateStream(stream)) {
      throw new Error('Invalid or inactive stream')
    }
    
    state.isLoading = true
    state.hasError = false
    
    videoLogger.logStreamInfo(participantId, stream, 'Remote assignment start')
    videoLogger.logVideoElementState(participantId, element, 'Before remote assignment')
    
    const result: StreamAssignmentResult = await streamManager.assignStreamToElement(
      element, 
      stream, 
      participantId,
      {
        maxRetries: 3,
        retryDelay: 1000,
        timeout: 5000,
        enableLogging: true
      }
    )
    
    if (result.success) {
      state.isLoading = false
      state.isPlaying = true
      state.retryCount = result.retryCount
      state.errorMessage = undefined
      
      const totalDuration = endVideoTiming(timingKey, 'REMOTE_STREAM', `Remote stream assignment completed for ${participantId}`, participantId)
      
      videoLogger.info('REMOTE_STREAM', `Successfully assigned stream to ${participantId}`, {
        retryCount: result.retryCount,
        duration: result.duration,
        totalDuration
      }, participantId)
      
      videoLogger.updateMetrics(participantId, {
        streamAssignmentTime: totalDuration,
        retryCount: result.retryCount,
        successRate: 100
      })
      
      videoLogger.logVideoElementState(participantId, element, 'After successful remote assignment')
    } else {
      throw result.error || new Error('Stream assignment failed after all retries')
    }
    
  } catch (error) {
    const duration = endVideoTiming(timingKey, 'REMOTE_STREAM', `Remote stream assignment failed for ${participantId}`, participantId)
    
    videoLogger.error('REMOTE_STREAM', `Failed to assign stream to ${participantId}`, error, participantId)
    
    state.isLoading = false
    state.hasError = true
    state.errorMessage = error instanceof Error ? error.message : 'Unknown error'
    
    // Update retry count from the result if available
    if (error instanceof Error && 'retryCount' in error) {
      state.retryCount = (error as any).retryCount || 0
    }
    
    videoLogger.updateMetrics(participantId, {
      errorCount: 1,
      retryCount: state.retryCount,
      successRate: 0
    })
    
    videoLogger.logVideoElementState(participantId, element, 'After failed remote assignment')
    
    throw error
  }
}

// Get or create video state for participant
function getVideoState(participantId: string): VideoState {
  if (!remoteVideoStates.value.has(participantId)) {
    remoteVideoStates.value.set(participantId, {
      isLoading: false,
      hasError: false,
      retryCount: 0,
      lastRetryTime: 0,
      isPlaying: false
    })
  }
  return remoteVideoStates.value.get(participantId)!
}

// Event handlers with detailed logging
function onLocalVideoLoaded() {
  videoLogger.info('LOCAL_VIDEO', 'Local video loaded successfully', {
    readyState: localVideoRef.value?.readyState,
    videoWidth: localVideoRef.value?.videoWidth,
    videoHeight: localVideoRef.value?.videoHeight
  })
  
  localVideoState.isLoading = false
  localVideoState.isPlaying = true
  localVideoState.hasError = false
  
  if (localVideoRef.value) {
    videoLogger.logVideoElementState('local', localVideoRef.value, 'Video loaded event')
  }
}

function onLocalVideoError(event: Event) {
  videoLogger.error('LOCAL_VIDEO', 'Local video error occurred', event)
  
  localVideoState.isLoading = false
  localVideoState.hasError = true
  localVideoState.errorMessage = 'Failed to load local video'
  
  if (localVideoRef.value) {
    videoLogger.logVideoElementState('local', localVideoRef.value, 'Video error event')
  }
}

function onRemoteVideoLoaded(participantId: string) {
  const element = remoteVideoRefs.value.get(participantId)
  
  videoLogger.info('REMOTE_VIDEO', `Remote video loaded for participant: ${participantId}`, {
    readyState: element?.readyState,
    videoWidth: element?.videoWidth,
    videoHeight: element?.videoHeight,
    duration: element?.duration
  }, participantId)
  
  const state = getVideoState(participantId)
  state.isLoading = false
  state.isPlaying = true
  state.hasError = false
  
  if (element) {
    videoLogger.logVideoElementState(participantId, element, 'Video loaded event')
  }
  
  videoLogger.updateMetrics(participantId, { videoLoadTime: performance.now() })
}

function onRemoteVideoError(participantId: string, event: Event) {
  const element = remoteVideoRefs.value.get(participantId)
  
  videoLogger.error('REMOTE_VIDEO', `Remote video error for participant ${participantId}`, event, participantId)
  
  const state = getVideoState(participantId)
  state.isLoading = false
  state.hasError = true
  state.errorMessage = 'Failed to load video stream'
  
  if (element) {
    videoLogger.logVideoElementState(participantId, element, 'Video error event')
  }
  
  videoLogger.updateMetrics(participantId, { errorCount: (videoLogger.getMetrics(participantId)?.errorCount || 0) + 1 })
}

function onRemoteVideoLoadStart(participantId: string) {
  const element = remoteVideoRefs.value.get(participantId)
  
  videoLogger.debug('REMOTE_VIDEO', `Remote video load started for participant: ${participantId}`, {
    networkState: element?.networkState,
    readyState: element?.readyState
  }, participantId)
  
  const state = getVideoState(participantId)
  state.isLoading = true
  state.hasError = false
  
  if (element) {
    videoLogger.logVideoElementState(participantId, element, 'Video load start event')
  }
}

// Enhanced retry connection with better logic
async function retryConnection(participantId: string) {
  const state = getVideoState(participantId)
  const now = Date.now()
  
  // Prevent too frequent retries (minimum 2 seconds between retries)
  if (now - state.lastRetryTime < 2000) {
    console.log(`[VideoGrid] Retry too soon for ${participantId}, waiting...`)
    return
  }
  
  // Check if we've exceeded max retries
  if (state.retryCount >= 5) {
    console.warn(`[VideoGrid] Max retries exceeded for participant ${participantId}`)
    state.errorMessage = 'Connection failed after multiple attempts'
    return
  }
  
  state.lastRetryTime = now
  
  console.log(`[VideoGrid] Manual retry for participant ${participantId} (previous attempts: ${state.retryCount})`)
  
  const videoElement = remoteVideoRefs.value.get(participantId)
  const stream = meetingStore.remoteStreams.get(participantId)
  
  if (!videoElement) {
    console.error(`[VideoGrid] No video element found for ${participantId}`)
    return
  }
  
  if (!stream) {
    console.error(`[VideoGrid] No stream found for ${participantId}`)
    state.errorMessage = 'No video stream available'
    return
  }
  
  // Validate stream before retry
  if (!streamManager.validateStream(stream)) {
    console.error(`[VideoGrid] Invalid stream for ${participantId}`)
    state.errorMessage = 'Video stream is not active'
    return
  }
  
  try {
    // Clean up previous assignment
    streamManager.cleanupElement(videoElement, participantId)
    
    // Reset state for retry
    state.hasError = false
    state.errorMessage = undefined
    
    // Attempt new assignment
    await assignStreamToElement(videoElement, stream, participantId)
    
    console.log(`[VideoGrid] Manual retry successful for ${participantId}`)
    
  } catch (error) {
    console.error(`[VideoGrid] Manual retry failed for ${participantId}:`, error)
    state.hasError = true
    state.errorMessage = error instanceof Error ? error.message : 'Retry failed'
  }
}

// Utility functions
function getParticipantInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

function getConnectionQuality(participantId: string): string {
  const state = getVideoState(participantId)
  if (state.hasError) return 'poor'
  if (state.isLoading) return 'connecting'
  if (state.isPlaying) return 'good'
  return 'unknown'
}

// Watchers for stream changes
watch(localStream, async (newStream) => {
  if (localVideoRef.value) {
    localVideoState.isLoading = true
    try {
      if (newStream) {
        await assignStreamToElement(localVideoRef.value, newStream)
      } else {
        localVideoRef.value.srcObject = null
        localVideoState.isPlaying = false
      }
    } catch (error) {
      console.error('Failed to assign local stream:', error)
      localVideoState.hasError = true
    }
  }
}, { immediate: true })

// Watch for remote stream changes with improved handling
watch(() => meetingStore.remoteStreams, (streams, oldStreams) => {
  console.log('Remote streams changed:', streams.size, 'streams')
  
  nextTick(async () => {
    // Handle new or updated streams
    for (const [participantId, stream] of streams) {
      const videoElement = remoteVideoRefs.value.get(participantId)
      const oldStream = oldStreams?.get(participantId)
      
      // Only assign if element exists and stream is new or changed
      if (videoElement && stream !== oldStream) {
        try {
          await assignStreamToElement(videoElement, stream, participantId)
        } catch (error) {
          console.error(`Failed to assign stream for participant ${participantId}:`, error)
        }
      }
    }
    
    // Handle removed streams
    if (oldStreams) {
      for (const [participantId] of oldStreams) {
        if (!streams.has(participantId)) {
          const videoElement = remoteVideoRefs.value.get(participantId)
          if (videoElement) {
            videoElement.srcObject = null
          }
          remoteVideoStates.value.delete(participantId)
        }
      }
    }
  })
}, { deep: true, immediate: true })

// Enhanced cleanup on unmount
onUnmounted(() => {
  console.log('[VideoGrid] Cleaning up video grid component')
  
  // Clean up local video
  if (localVideoRef.value) {
    streamManager.cleanupElement(localVideoRef.value, 'local')
  }
  
  // Clean up all remote videos
  remoteVideoRefs.value.forEach((element, participantId) => {
    streamManager.cleanupElement(element, participantId)
  })
  
  // Clear all references and states
  remoteVideoRefs.value.clear()
  remoteVideoStates.value.clear()
  
  console.log('[VideoGrid] Cleanup completed')
})
</script>

<style scoped>
.video-grid {
  @apply grid gap-4 h-full w-full p-6;
}

.video-tile {
  @apply relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl overflow-hidden min-h-[200px] shadow-lg border border-gray-700/50;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-tile:hover {
  @apply shadow-xl border-gray-600/50 transform scale-[1.02];
}

.video-element {
  @apply w-full h-full object-contain;
}

.video-overlay {
  @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4;
}

.participant-info {
  @apply flex justify-between items-center;
}

.participant-name {
  @apply text-white font-semibold text-sm drop-shadow-lg;
}

.media-indicators {
  @apply flex gap-2 items-center;
}

.media-indicators i {
  @apply text-sm drop-shadow-lg;
}

.media-indicators .muted {
  @apply text-red-400;
}

.media-indicators .video-off {
  @apply text-red-400;
}

.media-indicators .screen-share {
  @apply text-blue-400;
}

.connection-indicator {
  @apply w-2 h-2 rounded-full;
}

.connection-indicator.good {
  @apply bg-green-400;
}

.connection-indicator.poor {
  @apply bg-red-400;
}

.connection-indicator.connecting {
  @apply bg-yellow-400 animate-pulse;
}

.connection-indicator.unknown {
  @apply bg-gray-400;
}

.loading-overlay {
  @apply absolute inset-0 bg-gray-900/90 flex flex-col items-center justify-center text-white;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-white/20 border-t-white rounded-full animate-spin mb-3;
}

.error-overlay {
  @apply absolute inset-0 bg-red-900/90 flex flex-col items-center justify-center text-white text-center p-4;
}

.error-overlay i {
  @apply text-3xl mb-2 text-red-300;
}

.retry-button {
  @apply mt-3 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm font-medium transition-colors;
}

.no-video-overlay {
  @apply absolute inset-0 bg-gray-800 flex flex-col items-center justify-center text-white;
}

.avatar-placeholder {
  @apply w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl mb-3 shadow-lg;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .video-grid {
    @apply gap-2 p-3;
  }
  
  .video-tile {
    @apply min-h-[150px];
  }
  
  .video-overlay {
    @apply p-2;
  }
  
  .participant-name {
    @apply text-xs;
  }
  
  .media-indicators i {
    @apply text-xs;
  }
}

/* Animation for layout changes */
.video-tile {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>