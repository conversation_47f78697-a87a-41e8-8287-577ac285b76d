package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	lua "github.com/yuin/gopher-lua"
)

// 客户端连接结构
type Client struct {
	ID        string
	Conn      *websocket.Conn
	Send      chan []byte
	Server    *Server
	LuaState  *lua.LState
	Connected bool
}

// 服务器结构
type Server struct {
	NodeID       string
	Clients      map[string]*Client
	Register     chan *Client
	Unregister   chan *Client
	Broadcast    chan []byte
	LuaState     *lua.LState
	APIMap       map[string]lua.LGFunction
	ClusterNodes map[string]string
	mu           sync.RWMutex
	shuttingDown bool
}

// 消息结构
type Message struct {
	Type   string      `json:"type"`
	Data   interface{} `json:"data"`
	Client string      `json:"client,omitempty"`
	Node   string      `json:"node,omitempty"`
	Script string      `json:"script,omitempty"`
}

// 升级HTTP连接为WebSocket
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// 创建新服务器实例
func NewServer(nodeID string) *Server {
	ls := lua.NewState()
	return &Server{
		NodeID:       nodeID,
		Clients:      make(map[string]*Client),
		Register:     make(chan *Client),
		Unregister:   make(chan *Client),
		Broadcast:    make(chan []byte),
		LuaState:     ls,
		APIMap:       make(map[string]lua.LGFunction),
		ClusterNodes: make(map[string]string),
	}
}

// 处理WebSocket连接
func (s *Server) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	if s.shuttingDown {
		http.Error(w, "服务器正在关闭", http.StatusServiceUnavailable)
		return
	}

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("WebSocket升级失败:", err)
		return
	}

	client := &Client{
		ID:        generateClientID(),
		Conn:      conn,
		Send:      make(chan []byte, 256),
		Server:    s,
		LuaState:  lua.NewState(),
		Connected: true,
	}

	// 注册客户端
	s.Register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

// 客户端读取协程
func (c *Client) readPump() {
	defer func() {
		c.Server.Unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(512000) // 512KB
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		// 处理接收到的消息
		c.handleMessage(message)
	}
}

// 客户端写入协程
func (c *Client) writePump() {
	ticker := time.NewTicker(30 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// 通道关闭，发送关闭消息
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}

			w.Write(message)

			// 检查是否有其他消息需要发送
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// 处理客户端消息
func (c *Client) handleMessage(message []byte) {
	var msg Message
	if err := json.Unmarshal(message, &msg); err != nil {
		log.Printf("消息解析失败: %v", err)
		return
	}

	// 根据消息类型处理
	switch msg.Type {
	case "script":
		// 执行Lua脚本
		script, ok := msg.Data.(string)
		if !ok {
			log.Println("脚本格式错误")
			return
		}

		// 在独立的Lua环境中执行脚本
		go c.executeLuaScript(script)

	case "api":
		// 调用API
		apiData, ok := msg.Data.(map[string]interface{})
		if !ok {
			log.Println("API调用格式错误")
			return
		}

		apiName, ok := apiData["name"].(string)
		if !ok {
			log.Println("API名称缺失")
			return
		}

		// 调用API
		c.callAPI(apiName, apiData["params"])

	default:
		// 广播消息
		c.Server.Broadcast <- message
	}
}

// 执行Lua脚本
func (c *Client) executeLuaScript(script string) {
	// 保护客户端Lua环境
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Lua脚本执行错误: %v", r)
			errMsg := Message{
				Type: "error",
				Data: fmt.Sprintf("Lua脚本执行错误: %v", r),
			}
			msgBytes, _ := json.Marshal(errMsg)
			c.Send <- msgBytes
		}
	}()

	// 创建一个新的Lua状态
	L := lua.NewState()
	defer L.Close()

	// 注册API
	RegisterLuaAPI(L)

	// 将客户端对象传递给Lua环境
	ud := L.NewUserData()
	ud.Value = c
	L.SetGlobal("client", ud)

	// 执行脚本
	if err := L.DoString(script); err != nil {
		log.Printf("Lua脚本执行失败: %v", err)
		errMsg := Message{
			Type: "error",
			Data: fmt.Sprintf("Lua脚本执行失败: %v", err),
		}
		msgBytes, _ := json.Marshal(errMsg)
		c.Send <- msgBytes
	}
}

// 调用API
func (c *Client) callAPI(apiName string, params interface{}) {
	// 在服务器的API映射中查找
	apiFunc, exists := c.Server.APIMap[apiName]
	if !exists {
		log.Printf("未找到API: %s", apiName)
		errMsg := Message{
			Type: "error",
			Data: fmt.Sprintf("未找到API: %s", apiName),
		}
		msgBytes, _ := json.Marshal(errMsg)
		c.Send <- msgBytes
		return
	}

	// 创建一个新的Lua状态来执行API
	L := lua.NewState()
	defer L.Close()

	// 注册API
	RegisterLuaAPI(L)

	// 将客户端对象传递给Lua环境
	ud := L.NewUserData()
	ud.Value = c
	L.SetGlobal("client", ud)

	// 调用API
	L.Push(apiFunc)

	// 处理参数
	if params != nil {
		// 将参数转换为Lua值
		pushValueToLua(L, params)
	}

	// 执行函数
	if err := L.PCall(1, lua.MultRet, 0); err != nil {
		log.Printf("API调用失败: %v", err)
		errMsg := Message{
			Type: "error",
			Data: fmt.Sprintf("API调用失败: %v", err),
		}
		msgBytes, _ := json.Marshal(errMsg)
		c.Send <- msgBytes
	}
}

// 将Go值转换为Lua值
func pushValueToLua(L *lua.LState, value interface{}) {
	switch v := value.(type) {
	case nil:
		L.Push(lua.LNil)
	case bool:
		L.Push(lua.LBool(v))
	case int:
		L.Push(lua.LNumber(v))
	case int64:
		L.Push(lua.LNumber(v))
	case float32:
		L.Push(lua.LNumber(v))
	case float64:
		L.Push(lua.LNumber(v))
	case string:
		L.Push(lua.LString(v))
	case []interface{}:
		tbl := L.NewTable()
		for i, item := range v {
			L.Push(lua.LNumber(i + 1))
			pushValueToLua(L, item)
			L.SetTable(tbl)
		}
		L.Push(tbl)
	case map[string]interface{}:
		tbl := L.NewTable()
		for k, v := range v {
			L.Push(lua.LString(k))
			pushValueToLua(L, v)
			L.SetTable(tbl)
		}
		L.Push(tbl)
	default:
		L.Push(lua.LNil)
	}
}

// 加载Lua脚本
func (s *Server) LoadScripts(dir string) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return fmt.Errorf("读取脚本目录失败: %v", err)
	}

	for _, file := range files {
		if !file.IsDir() && (strings.HasSuffix(file.Name(), ".lua") || strings.HasSuffix(file.Name(), ".lc")) {
			filePath := filepath.Join(dir, file.Name())
			if err := s.LuaState.DoFile(filePath); err != nil {
				return fmt.Errorf("执行Lua脚本 %s 失败: %v", filePath, err)
			}
			log.Printf("已加载Lua脚本: %s", filePath)
		}
	}

	return nil
}

// 加入集群
func (s *Server) JoinCluster(url string) error {
	// 实现集群连接逻辑
	// ...

	return nil
}

// 关闭服务器
func (s *Server) Shutdown() {
	s.mu.Lock()
	s.shuttingDown = true
	s.mu.Unlock()

	// 关闭所有客户端连接
	close(s.Register)
	close(s.Unregister)
	close(s.Broadcast)

	// 清理资源
	s.LuaState.Close()

	for _, client := range s.Clients {
		close(client.Send)
		client.Conn.Close()
	}
}

// 生成客户端ID
func generateClientID() string {
	return uuid.New().String()
}
