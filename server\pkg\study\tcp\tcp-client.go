package tcp

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"sync/atomic"
	"time"
)

const (
	serverAddr        = "localhost:9999"
	maxRetries        = 5
	retryDelay        = 3 * time.Second
	heartbeatInterval = 5 * time.Second
	heartbeatTimeout  = 10 * time.Second
)

func connectToServer() (net.Conn, error) {
	return net.Dial("tcp", serverAddr)
}

type Conn struct {
	conn          net.Conn
	lastWriteTime time.Time
	lastReadTime  int64
}

// 使用atomic代替锁
func (c *Conn) UpdateLastWriteTime() {
	atomic.StoreInt64(&c.lastReadTime, time.Now().UnixNano())
}

func (c *Conn) GetLastWriteTime() time.Time {
	return time.Unix(0, atomic.LoadInt64(&c.lastReadTime))
}

func (c *Conn) Write(p []byte) (int, error) {
	n, err := c.conn.Write(p)
	if err == nil {
		c.lastWriteTime = time.Now()
	}
	return n, err
}

func (c *Conn) HeartBeat() (int, error) {
	keepAlive := make(chan bool)
	go func() {
		ticker := time.NewTicker(heartbeatInterval)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				fmt.Println(c.lastWriteTime.String())
				if c.lastWriteTime.IsZero() || time.Since(c.lastWriteTime) >= heartbeatInterval {
					if _, err := c.Write([]byte("Heartbeat\n")); err != nil {
						fmt.Println("Error sending heartbeat:", err.Error())
						close(keepAlive)
						return
					}
				}

			case <-keepAlive:
				// Keep the connection alive
			}
		}
	}()

	return 0, nil
}

func (c *Conn) handleConnection() {
	defer c.conn.Close()
	c.HeartBeat()
	go func() {
		for i := 1; ; i++ {
			msg := fmt.Sprintf("Message %d\n", i)
			_, err := c.Write([]byte(msg))
			if err != nil {
				fmt.Println("Error writing to server:", err.Error())
				return
			}
			time.Sleep(2 * time.Second) // 每两秒发送一次消息
		}
	}()
	reader := bufio.NewReader(c.conn)
	for {
		message, err := reader.ReadString('\n')
		if err != nil {
			fmt.Println("Error reading from server:", err.Error())
			return
		}
		fmt.Print("Message from server:", message)
	}
}

func main() {
	var retries int
	for {
		conn, err := connectToServer()
		if err != nil {
			fmt.Println("Failed to connect to server:", err.Error())
			retries++
			if retries > maxRetries {
				fmt.Println("Max retries reached. Exiting.")
				os.Exit(1)
			}
			fmt.Printf("Retrying in %v...\n", retryDelay)
			time.Sleep(retryDelay)
			continue
		}
		retries = 0 // 重置重试计数
		c := &Conn{conn: conn}
		c.handleConnection()
		fmt.Println("Connection closed. Reconnecting...")
	}
}
