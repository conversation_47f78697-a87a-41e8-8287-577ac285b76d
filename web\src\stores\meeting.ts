import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { WebRTCService } from '@/services/WebRTCService'
import type { Participant, ChatMessage, FileTransfer, MediaState } from '@/types/webrtc'

export const useMeetingStore = defineStore('meeting', () => {
  // State
  const webrtcService = ref<WebRTCService | null>(null)
  const participants = ref<Map<string, Participant>>(new Map())
  const chatMessages = ref<ChatMessage[]>([])
  const fileTransfers = ref<FileTransfer[]>([])
  const localStream = ref<MediaStream | null>(null)
  const screenStream = ref<MediaStream | null>(null)
  const remoteStreams = ref<Map<string, MediaStream>>(new Map())
  const isConnected = ref(false)
  const isJoining = ref(false)
  const currentUser = ref<Participant | null>(null)
  const roomId = ref('')
  const clientId = ref('')

  // Additional state for HomeView.vue.bak compatibility
  const connectionState = ref<'connecting' | 'connected' | 'disconnected'>('disconnected')
  const inMeeting = ref(false)
  const displayName = ref('')
  const localDeviceId = ref('')
  const currentMeeting = ref<any>(null)
  const isHost = ref(false)
  const localMediaState = ref({ microphone: true, camera: true, screenSharing: false })
  const participantCount = ref(0)
  const isRecording = ref(false)
  const unreadMessages = ref(0)
  const showChatPanel = ref(false)

  // Computed
  const participantsList = computed(() => Array.from(participants.value.values()))
  const unreadMessagesCount = computed(() => chatMessages.value.filter((msg) => !msg.read).length)
  const activeTransfers = computed(() =>
    fileTransfers.value.filter((t) => t.status === 'transferring')
  )

  // Actions
  async function joinMeeting(roomIdParam: string, clientIdParam: string, wsUrl: string) {
    if (isJoining.value) return

    isJoining.value = true
    roomId.value = roomIdParam
    clientId.value = clientIdParam

    try {
      // Create WebRTC service
      webrtcService.value = new WebRTCService(clientIdParam, roomIdParam)

      // Setup event handlers
      setupEventHandlers()

      // Connect to signaling server
      await webrtcService.value.connect(wsUrl)

      // Create current user
      currentUser.value = {
        id: clientIdParam,
        name: `User ${clientIdParam.slice(0, 8)}`,
        mediaState: { video: false, audio: false, screen: false }
      }

      participants.value.set(clientIdParam, currentUser.value)
      isConnected.value = true
    } catch (error) {
      console.error('Failed to join meeting:', error)
      throw error
    } finally {
      isJoining.value = false
    }
  }

  function setupEventHandlers() {
    if (!webrtcService.value) return

    webrtcService.value.onParticipantJoined = (participant: Participant) => {
      participants.value.set(participant.id, participant)
    }

    webrtcService.value.onParticipantLeft = (participantId: string) => {
      participants.value.delete(participantId)
      remoteStreams.value.delete(participantId)
    }

    webrtcService.value.onRemoteStream = (participantId: string, stream: MediaStream) => {
      remoteStreams.value.set(participantId, stream)
    }

    webrtcService.value.onChatMessage = (message: ChatMessage) => {
      chatMessages.value.push(message)
    }

    webrtcService.value.onFileReceived = (file: File) => {
      // Create download link
      const url = URL.createObjectURL(file)
      const a = document.createElement('a')
      a.href = url
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      // Add to chat as file message
      chatMessages.value.push({
        id: Date.now().toString(),
        senderId: 'system',
        senderName: 'System',
        content: `File received: ${file.name}`,
        timestamp: Date.now(),
        type: 'file'
      })
    }

    webrtcService.value.onFileProgress = (transfer: FileTransfer) => {
      const existingIndex = fileTransfers.value.findIndex((t) => t.id === transfer.id)
      if (existingIndex >= 0) {
        fileTransfers.value[existingIndex] = transfer
      } else {
        fileTransfers.value.push(transfer)
      }
    }

    webrtcService.value.onMediaStateChanged = (participantId: string, mediaState: MediaState) => {
      const participant = participants.value.get(participantId)
      if (participant) {
        participant.mediaState = mediaState
      }
    }
  }

  async function startCamera() {
    if (!webrtcService.value) return

    try {
      localStream.value = await webrtcService.value.startCamera()
      if (currentUser.value) {
        currentUser.value.mediaState.video = true
        currentUser.value.mediaState.audio = true
        currentUser.value.stream = localStream.value
      }
    } catch (error) {
      console.error('Failed to start camera:', error)
      throw error
    }
  }

  async function startScreenShare() {
    if (!webrtcService.value) return

    try {
      screenStream.value = await webrtcService.value.startScreenShare()
      if (currentUser.value) {
        currentUser.value.mediaState.screen = true
      }
    } catch (error) {
      console.error('Failed to start screen share:', error)
      throw error
    }
  }

  async function stopScreenShare() {
    if (!webrtcService.value) return

    await webrtcService.value.stopScreenShare()
    screenStream.value = null
    if (currentUser.value) {
      currentUser.value.mediaState.screen = false
    }
  }

  function toggleAudio() {
    if (!webrtcService.value || !currentUser.value) return false

    const enabled = webrtcService.value.toggleAudio()
    currentUser.value.mediaState.audio = enabled
    return enabled
  }

  function toggleVideo() {
    if (!webrtcService.value || !currentUser.value) return false

    const enabled = webrtcService.value.toggleVideo()
    currentUser.value.mediaState.video = enabled
    return enabled
  }

  function sendChatMessage(content: string) {
    if (!webrtcService.value) return

    webrtcService.value.sendChatMessage(content)
  }

  async function sendFile(file: File) {
    if (!webrtcService.value) return

    try {
      await webrtcService.value.sendFile(file)
    } catch (error) {
      console.error('Failed to send file:', error)
      throw error
    }
  }

  function leaveMeeting() {
    if (webrtcService.value) {
      webrtcService.value.disconnect()
    }

    // Reset state
    participants.value.clear()
    chatMessages.value = []
    fileTransfers.value = []
    remoteStreams.value.clear()
    localStream.value = null
    screenStream.value = null
    isConnected.value = false
    currentUser.value = null
    webrtcService.value = null
  }

  function markMessagesAsRead() {
    chatMessages.value.forEach((msg) => {
      msg.read = true
    })
  }

  // Additional methods for HomeView.vue.bak compatibility
  function setConnectionState(state: 'connecting' | 'connected' | 'disconnected') {
    connectionState.value = state
  }

  function setMeeting(meeting: any) {
    currentMeeting.value = meeting
    inMeeting.value = true
  }

  function resetMeeting() {
    currentMeeting.value = null
    inMeeting.value = false
    isHost.value = false
    participants.value.clear()
    chatMessages.value = []
    remoteStreams.value.clear()
  }

  function addParticipant(participant: any) {
    participants.value.set(participant.id, participant)
    participantCount.value = participants.value.size
  }

  function removeParticipant(participantId: string) {
    participants.value.delete(participantId)
    participantCount.value = participants.value.size
  }

  function addRemoteStream(streamInfo: any) {
    remoteStreams.value.set(streamInfo.id, streamInfo.stream)
  }

  function updateParticipantMediaState(participantId: string, mediaState: any) {
    const participant = participants.value.get(participantId)
    if (participant) {
      participant.mediaState = { ...participant.mediaState, ...mediaState }
    }
  }

  function addChatMessage(message: any) {
    chatMessages.value.push(message)
    if (!showChatPanel.value) {
      unreadMessages.value++
    }
  }

  function clearChatUnread() {
    unreadMessages.value = 0
  }

  function updateFileTransferProgress(participantId: string, progress: any) {
    // Implementation for file transfer progress
  }

  return {
    // State
    participants,
    chatMessages,
    fileTransfers,
    localStream,
    screenStream,
    remoteStreams,
    isConnected,
    isJoining,
    currentUser,
    roomId,
    clientId,

    // Additional state
    connectionState,
    inMeeting,
    displayName,
    localDeviceId,
    currentMeeting,
    isHost,
    localMediaState,
    participantCount,
    isRecording,
    unreadMessages,
    showChatPanel,

    // Computed
    participantsList,
    unreadMessagesCount,
    activeTransfers,

    // Actions
    joinMeeting,
    startCamera,
    startScreenShare,
    stopScreenShare,
    toggleAudio,
    toggleVideo,
    sendChatMessage,
    sendFile,
    leaveMeeting,
    markMessagesAsRead,

    // Additional methods
    setConnectionState,
    setMeeting,
    resetMeeting,
    addParticipant,
    removeParticipant,
    addRemoteStream,
    updateParticipantMediaState,
    addChatMessage,
    clearChatUnread,
    updateFileTransferProgress
  }
})
