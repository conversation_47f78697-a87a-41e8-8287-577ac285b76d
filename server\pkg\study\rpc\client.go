package main

import (
	"fmt"
	"github.com/smallnest/rpcx/codec"
	"os"
)

type Args struct {
	A int
	B int
}
type Reply struct {
	C int
}

type Arith int

func main() {
	cc := &codec.MsgpackCodec{}
	b, err := cc.Encode("test")
	fmt.Printf("%s %v", b, err)
	os.Exit(0)
	//d, _ := client.NewPeer2PeerDiscovery("tcp@localhost:8972", "")
	//// #4
	//xclient := client.NewXClient("Arith", client.Failover, client.RoundRobin, d, client.DefaultOption)
	//defer xclient.Close()
	//args := &Args{
	//	A: 10,
	//	B: 20,
	//}
	//
	//reply := &Reply{}
	//err := xclient.Call(context.Background(), "Mul", args, reply)
	//if err != nil {
	//	log.Fatalf("failed to call: %v", err)
	//}
	//
	//fmt.Println(reply.C)
}
