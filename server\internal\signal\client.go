package signal

import (
	"math"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer.
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer.
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait.
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer.
	maxMessageSize = 10240
)

var (
	newline     = []byte{'\n'}
	space       = []byte{' '}
	pingMessage = []byte("PING")
	pongMessage = []byte("PONG")
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  10240,
	WriteBufferSize: 10240,
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow connections from any origin
	},
}

type Role uint8

const (
	RoleMaster Role = iota + 1
	RoleUser
)

const RoleAll Role = math.MaxUint8

// Client is a middleman between the websocket connection and the room.
type Client struct {
	ID string `json:"id"`
	// We'll use an interface to avoid import cycles
	room *Room

	// The websocket connection.
	conn *websocket.Conn

	// Buffered channel of outbound messages.
	send chan *Message

	// Client role (bitmap)
	role Role
}

// newClient creates a new client with a specific role
func newClient(conn *websocket.Conn, room *Room, clientID string, role Role) *Client {
	return &Client{
		ID:   clientID,
		room: room,
		conn: conn,
		send: make(chan *Message, 256),
		role: role,
	}
}

// Send sends a message to the client
func (c *Client) Send(msg *Message) {
	c.send <- msg
}

func (c *Client) HasRole(role Role) bool {
	return (c.role & role) != 0
}

func (c *Client) newMessage(t MessageType, data any, receiver Receiver) *Message {
	if receiver == nil {
		receiver = &RoleReceiver{Role: RoleAll}
	}
	return &Message{
		Type:     t,
		Data:     data,
		From:     c,
		receiver: receiver,
	}
}
