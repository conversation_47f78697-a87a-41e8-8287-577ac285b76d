package services

import (
	"context"
	"meeting/internal/repository"
	"meeting/internal/service"
	"meeting/pkg/core"
	"sync"

	"go.uber.org/zap"
)

var (
	once           sync.Once
	serviceManager *Manager
)

// Manager 服务管理器
type Manager struct {
	AuthService    *service.AuthService
	StickerService service.StickerService
	UserService    service.UserService
	MCPService     service.MCPService
	APIKeyService  *service.APIKeyService
	initialized    bool
	mu             sync.RWMutex
}

// GetServiceManager 获取服务管理器单例
func GetServiceManager() *Manager {
	once.Do(func() {
		serviceManager = &Manager{}
	})
	return serviceManager
}

func (m *Manager) initializeServices() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		return
	}

	// 创建一个简单的logger
	logger, _ := zap.NewDevelopment()
	sugar := logger.Sugar()

	// 使用全局DB连接
	db := core.DB(context.Background())

	// 创建仓库
	userRepo := repository.NewUserRepository(db)
	stickerRepo := repository.NewStickerRepository(db)

	// 创建服务
	m.AuthService = service.NewAuthService(userRepo, sugar)
	m.StickerService = service.NewStickerService(stickerRepo, sugar)
	m.UserService = service.NewUserService(userRepo, sugar)
	m.MCPService = service.NewMCPService(sugar)
	m.APIKeyService = service.NewAPIKeyService()

	m.initialized = true
}

func (m *Manager) GetAuthService() *service.AuthService {
	m.mu.RLock()
	if !m.initialized {
		m.mu.RUnlock()
		m.initializeServices()
		m.mu.RLock()
	}
	defer m.mu.RUnlock()
	return m.AuthService
}

func (m *Manager) GetStickerService() service.StickerService {
	m.mu.RLock()
	if !m.initialized {
		m.mu.RUnlock()
		m.initializeServices()
		m.mu.RLock()
	}
	defer m.mu.RUnlock()
	return m.StickerService
}

func (m *Manager) GetMCPService() service.MCPService {
	m.mu.RLock()
	if !m.initialized {
		m.mu.RUnlock()
		m.initializeServices()
		m.mu.RLock()
	}
	defer m.mu.RUnlock()
	return m.MCPService
}

// GetAPIKeyService 获取API密钥服务实例
func (m *Manager) GetAPIKeyService() *service.APIKeyService {
	m.mu.RLock()
	if !m.initialized {
		m.mu.RUnlock()
		m.initializeServices()
		m.mu.RLock()
	}
	defer m.mu.RUnlock()
	return m.APIKeyService
}
