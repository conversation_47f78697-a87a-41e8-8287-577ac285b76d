package geocode

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

// 高德地图 API Key，需要替换为你自己的 Key
const amapAPIKey = "d13e6198fc637a5e458625df70dd3a8a"

// 定义一个结构体来解析高德地图的响应
type AmapGeocodeResponse struct {
	Status   string `json:"status"`
	Info     string `json:"info"`
	Geocodes []struct {
		Location string `json:"location"`
	} `json:"geocodes"`
}

// 调用高德地图 API 获取经纬度
func getCoordinatesFromAmap(address string) (float64, float64, error) {
	apiURL := fmt.Sprintf("https://restapi.amap.com/v3/geocode/geo?address=%s&key=%s", address, amapAPIKey)
	resp, err := http.Get(apiURL)
	if err != nil {
		return 0, 0, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, 0, err
	}

	var amapResp AmapGeocodeResponse
	err = json.Unmarshal(body, &amapResp)
	if err != nil {
		return 0, 0, err
	}

	if amapResp.Status != "1" || len(amapResp.Geocodes) == 0 {
		return 0, 0, fmt.Errorf("failed to get coordinates: %s", amapResp.Info)
	}

	var longitude, latitude float64
	fmt.Sscanf(amapResp.Geocodes[0].Location, "%f,%f", &longitude, &latitude)
	return longitude, latitude, nil
}
